const fs = require('fs');

// 读取JavaScript文件
const jsContent = fs.readFileSync('script-5.js', 'utf8');
const lines = jsContent.split('\n');

console.log('Checking JavaScript syntax...');

// 尝试逐步构建代码来找到错误位置
let cumulativeCode = '';
let errorLine = -1;

for (let i = 0; i < lines.length; i++) {
  cumulativeCode += lines[i] + '\n';
  
  try {
    new Function(cumulativeCode);
  } catch (error) {
    console.log(`❌ Error found at line ${i + 1}:`);
    console.log(`Line content: "${lines[i]}"`);
    console.log(`Error: ${error.message}`);
    
    // 显示周围的代码
    console.log('\nContext:');
    for (let j = Math.max(0, i - 5); j <= Math.min(lines.length - 1, i + 5); j++) {
      const marker = j === i ? '>>> ' : '    ';
      console.log(`${marker}${j + 1}: ${lines[j]}`);
    }
    
    errorLine = i + 1;
    break;
  }
}

if (errorLine === -1) {
  console.log('✅ No syntax errors found');
} else {
  console.log(`\nFirst error at line ${errorLine}`);
}
