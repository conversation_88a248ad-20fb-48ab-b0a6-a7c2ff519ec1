<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>书籍列表 - 阅读记录</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link href="https://cdn.bootcdn.net/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css" rel="stylesheet">
  <script charset="UTF-8" id="LA_COLLECT" src="//sdk.51.la/js-sdk-pro.min.js"></script>
  <script>LA.init({id:"Jqud4Iv4jX9nBtrP",ck:"Jqud4Iv4jX9nBtrP"})</script>
  
  <!-- Tailwind 配置 -->
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: '#3B82F6',
            secondary: '#6366F1',
            success: '#10B981',
            danger: '#EF4444',
            warning: '#F59E0B',
            info: '#06B6D4',
            light: '#F3F4F6',
            dark: '#1F2937'
          },
          fontFamily: {
            inter: ['Inter', 'system-ui', 'sans-serif'],
          },
        },
      }
    }
  </script>
  
  <style type="text/tailwindcss">
    @layer utilities {
      .content-auto {
        content-visibility: auto;
      }
      .card-shadow {
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
      }
      .card-hover {
        @apply hover:shadow-lg transform hover:-translate-y-1 transition-all duration-300;
      }
      .btn-hover {
        @apply hover:shadow-lg transform hover:-translate-y-0.5 transition-all duration-300;
      }
      .status-badge {
        @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
      }
      .channel-badge {
        @apply inline-flex items-center px-2 py-1 rounded text-xs font-medium;
      }
    }
     /* 自动补全样式 */
      .suggestion-item {
        transition: all 0.2s ease;
      }
      .suggestion-item:hover {
        background-color: #eff6ff !important;
        border-left-color: #3b82f6 !important;
      }
      .suggestion-item mark {
        background-color: #fef3c7;
        color: #92400e;
        padding: 0 2px;
        border-radius: 2px;
      }

      /* 自动补全容器样式 */
      .autocomplete-container {
        border: 1px solid #e5e7eb;
        border-radius: 0.375rem;
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        max-height: 200px;
        overflow-y: auto;
        z-index: 1000;
      }
  </style>
</head>
<body class="bg-gradient-to-br from-blue-50 to-indigo-50 min-h-screen font-inter text-dark">
  <div class="container mx-auto px-4 py-8 max-w-7xl">
    <!-- 头部区域 -->
    <header class="mb-8">
      <div class="flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
          <h1 id="refreshButton" class="text-[clamp(1.8rem,4vw,2.8rem)] font-bold text-transparent bg-clip-text bg-gradient-to-r from-primary to-secondary mb-2 cursor-pointer">
            阅读
          </h1>
          <p class="text-gray-600 text-lg">
            小王这辈子能看多少书啊！
          </p>
        </div>
        <div class="mt-4 md:mt-0">
          <button id="add-book-btn" class="inline-flex items-center px-6 py-3 bg-primary text-white rounded-lg btn-hover">
            <i class="fa fa-plus mr-2"></i>添加新书
          </button>
        </div>
      </div>
    </header>

    <!-- 搜索和筛选区域 -->
    <div class="bg-white rounded-xl shadow-lg p-6 mb-8 card-shadow">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">搜索书籍</label>
          <input type="text" id="search-input" placeholder="书名或作者..." 
                 class="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-primary/50 focus:border-primary focus:outline-none transition-all">
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">阅读状态</label>
          <select id="status-filter" class="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-primary/50 focus:border-primary focus:outline-none transition-all">
            <option value="">全部状态</option>
            <option value="1">看完</option>
            <option value="0">在看</option>
            <option value="2">弃了</option>
          </select>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">阅读渠道</label>
          <select id="channel-filter" class="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-primary/50 focus:border-primary focus:outline-none transition-all">
            <option value="">全部渠道</option>
            <option value="1">微信读书</option>
            <option value="2">Readest</option>
            <option value="3">Kindle</option>
            <option value="4">实体书</option>
            <option value="5">图书馆</option>
            <option value="6">iPad</option>
            <option value="7">番茄</option>
          </select>
        </div>
        <div class="flex items-end">
          <button id="clear-filters" class="w-full px-4 py-2 bg-gray-200 text-gray-700 rounded-lg btn-hover">
            <i class="fa fa-refresh mr-2"></i>重置筛选
          </button>
        </div>
      </div>
    </div>

    <!-- 统计信息 -->
    <div class="grid grid-cols-1 md:grid-cols-6 gap-6 mb-8">
      <!-- 总书籍数 -->
      <div class="bg-white rounded-xl p-6 card-shadow">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-primary/10">
            <i class="fa fa-book text-primary text-xl"></i>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">总书籍数</p>
            <p class="text-2xl font-bold text-gray-900" id="total-books">0</p>
          </div>
        </div>
      </div>
      <!-- 新增：总字数卡片 -->
      <div class="bg-white rounded-xl p-6 card-shadow">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-info/10">
            <i class="fa fa-file-text-o text-info text-xl"></i>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">总字数</p>
            <p class="text-2xl font-bold text-gray-900" id="total-word-count">0</p>
          </div>
        </div>
      </div>
      <!-- 已读完 -->
      <div class="bg-white rounded-xl p-6 card-shadow">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-success/10">
            <i class="fa fa-check-circle text-success text-xl"></i>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">已读完</p>
            <p class="text-2xl font-bold text-gray-900" id="completed-books">0</p>
          </div>
        </div>
      </div>
      <div class="bg-white rounded-xl p-6 card-shadow">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-warning/10">
            <i class="fa fa-clock-o text-warning text-xl"></i>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">阅读中</p>
            <p class="text-2xl font-bold text-gray-900" id="reading-books">0</p>
          </div>
        </div>
      </div>
      <div class="bg-white rounded-xl p-6 card-shadow">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-danger/10">
            <i class="fa fa-times-circle text-danger text-xl"></i>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">弃了</p>
            <p class="text-2xl font-bold text-gray-900" id="abandoned-books">0</p>
          </div>
        </div>
      </div>
      <div class="bg-white rounded-xl p-6 card-shadow">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-info/10">
            <i class="fa fa-calendar text-info text-xl"></i>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">本月读完</p>
            <p class="text-2xl font-bold text-gray-900" id="monthly-books">0</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 书籍列表 -->
    <div class="bg-white rounded-xl shadow-lg overflow-hidden card-shadow">
      <div class="px-6 py-4 border-b border-gray-200">
        <h2 class="text-xl font-semibold text-gray-900 flex items-center">
          <i class="fa fa-list mr-2 text-primary"></i>书籍列表
          <span class="ml-2 text-sm font-normal text-gray-500" id="books-count">(0 本书)</span>
          <span id="cache-status" class="ml-2 text-sm font-normal text-gray-500"></span>
        </h2>
      </div>
      
      <!-- 加载状态 -->
      <div id="loading-state" class="p-8 text-center">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
        <p class="text-gray-600">正在加载书籍列表...</p>
      </div>
      
      <!-- 空状态 -->
      <div id="empty-state" class="p-8 text-center hidden">
        <div class="text-gray-400 mb-4">
          <i class="fa fa-book text-6xl"></i>
        </div>
        <h3 class="text-lg font-medium text-gray-900 mb-2">暂无书籍记录</h3>
        <p class="text-gray-600 mb-4">开始添加您的第一本书吧！</p>
        <button onclick="showAddBookModal()" class="inline-flex items-center px-4 py-2 bg-primary text-white rounded-lg btn-hover">
          <i class="fa fa-plus mr-2"></i>添加书籍
        </button>
      </div>
      
      <!-- 表格头部 -->
      <div id="table-header" class="px-6 py-3 bg-gray-50 border-b border-gray-200 hidden">
        <div class="grid grid-cols-1 lg:grid-cols-12 gap-4 items-center text-sm font-medium text-gray-700">
          <div class="lg:col-span-1">ID</div>
          <div class="lg:col-span-2">书籍信息</div>
          <div class="lg:col-span-1">字数</div>
          <div class="lg:col-span-1">状态</div>
          <div class="lg:col-span-1">渠道</div>
          <div class="lg:col-span-1">开始时间</div>
          <div class="lg:col-span-1">结束时间</div>
          <div class="lg:col-span-1">用时</div>
          <div class="lg:col-span-1">备注</div>
          <div class="lg:col-span-2">操作</div>
        </div>
      </div>

      <!-- 书籍卡片容器 -->
      <div id="books-container" class="p-6 space-y-4 hidden">
        <!-- 书籍卡片将通过JavaScript动态生成 -->
      </div>
    </div>

    <!-- 分页 -->
    <div id="pagination" class="mt-8 flex justify-center hidden">
      <nav class="flex items-center space-x-2">
        <button id="prev-page" class="px-3 py-2 text-gray-500 bg-white rounded-lg border border-gray-300 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
          <i class="fa fa-chevron-left"></i>
        </button>
        <div id="page-numbers" class="flex space-x-1">
          <!-- 页码按钮将通过JavaScript动态生成 -->
        </div>
        <button id="next-page" class="px-3 py-2 text-gray-500 bg-white rounded-lg border border-gray-300 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
          <i class="fa fa-chevron-right"></i>
        </button>
      </nav>
    </div>
  </div>

  <!-- 确认对话框 -->
  <div id="confirm-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-lg p-6 shadow-xl max-w-md w-full mx-4">
      <div class="flex items-center mb-4">
        <div class="p-3 rounded-full bg-warning/10 mr-4">
          <i class="fa fa-exclamation-triangle text-warning text-xl"></i>
        </div>
        <div>
          <h3 class="text-lg font-medium text-gray-900">确认操作</h3>
          <p class="text-gray-600 mt-1" id="confirm-message">确定要执行此操作吗？</p>
        </div>
      </div>
      <div class="flex justify-end space-x-3">
        <button id="confirm-cancel" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg btn-hover">
          取消
        </button>
        <button id="confirm-ok" class="px-4 py-2 bg-primary text-white rounded-lg btn-hover">
          确定
        </button>
      </div>
    </div>
  </div>

  <!-- 添加书籍模态窗口 -->
  <div id="add-book-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-xl shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
      <div class="p-6">
        <div class="flex items-center justify-between mb-6">
          <h2 class="text-2xl font-bold text-gray-900">添加新书</h2>
          <button id="close-add-modal" class="text-gray-400 hover:text-gray-600">
            <i class="fa fa-times text-xl"></i>
          </button>
        </div>
        
        <form id="add-book-form" class="space-y-6">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="relative">
              <label for="modal_book_name" class="block text-sm font-medium text-gray-700 mb-1">书籍名称 *</label>
              <input type="text" id="modal_book_name" name="book_name" placeholder="请输入书籍名称"
                     class="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-primary/50 focus:border-primary focus:outline-none transition-all" required autocomplete="off">
              <div id="modal_book_name_suggestions" class="absolute z-10 w-full bg-white border border-gray-300 rounded-lg shadow-lg max-h-48 overflow-y-auto hidden"></div>
            </div>

            <div class="relative">
              <label for="modal_author" class="block text-sm font-medium text-gray-700 mb-1">作者 *</label>
              <input type="text" id="modal_author" name="author" placeholder="请输入作者姓名"
                     class="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-primary/50 focus:border-primary focus:outline-none transition-all" required autocomplete="off">
              <div id="modal_author_suggestions" class="absolute z-10 w-full bg-white border border-gray-300 rounded-lg shadow-lg max-h-48 overflow-y-auto hidden"></div>
            </div>
            
            <div class="relative">
              <label for="modal_type" class="block text-sm font-medium text-gray-700 mb-1">书籍类型 *</label>
              <input type="text" id="modal_type" name="type" placeholder="例如: 商业、历史"
                     class="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-primary/50 focus:border-primary focus:outline-none transition-all" required autocomplete="off">
              <div id="modal_type_suggestions" class="absolute z-10 w-full bg-white border border-gray-300 rounded-lg shadow-lg max-h-48 overflow-y-auto hidden"></div>
            </div>
            
            <div>
              <label for="modal_channel" class="block text-sm font-medium text-gray-700 mb-1">阅读渠道 *</label>
              <select id="modal_channel" name="channel" 
                      class="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-primary/50 focus:border-primary focus:outline-none transition-all" required>
                <option value="1">微信读书</option>
                <option value="2">Readest</option>
                <option value="3">Kindle</option>
                <option value="4">实体书</option>
                <option value="5">图书馆</option>
                <option value="6">iPad</option>
                <option value="7">番茄小说</option>
              </select>
            </div>
            
            <div>
              <label for="modal_number_font" class="block text-sm font-medium text-gray-700 mb-1">字数</label>
              <input type="number" id="modal_number_font" name="number_font" placeholder="请输入字数"
                     class="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-primary/50 focus:border-primary focus:outline-none transition-all" min="0" step="any">
            </div>
          </div>

          <div>
            <label for="modal_remark" class="block text-sm font-medium text-gray-700 mb-1">备注</label>
            <textarea id="modal_remark" name="remark" rows="3" placeholder="请输入备注信息（可选）"
                      class="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-primary/50 focus:border-primary focus:outline-none transition-all resize-none"></textarea>
          </div>
          
          <div class="flex justify-end space-x-4 pt-4 border-t border-gray-100">
            <button type="button" id="cancel-add-book" class="px-6 py-2.5 bg-gray-200 text-gray-700 rounded-lg btn-hover">
              <i class="fa fa-times mr-2"></i>取消
            </button>
            <button type="submit" class="px-6 py-2.5 bg-primary text-white rounded-lg btn-hover">
              <i class="fa fa-save mr-2"></i>保存记录
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <!-- 加载遮罩 -->
  <div id="loading-overlay" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-lg p-6 shadow-xl max-w-md w-full">
      <div class="flex items-center">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mr-4"></div>
        <div>
          <h3 class="text-lg font-medium text-gray-900">处理中...</h3>
          <p class="text-gray-500 mt-1" id="loading-message">正在更新数据</p>
        </div>
      </div>
    </div>
  </div>

  <!-- 版本信息模态框 -->
  <div id="versionInfoModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden flex items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md mx-auto">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-semibold">版本信息</h2>
        <button id="closeVersionInfoModal" class="text-gray-500 hover:text-gray-700 focus:outline-none">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg>
        </button>
      </div>
      <div id="versionContent" class="mb-4 text-gray-800">
        <!-- 版本内容将通过 JavaScript 填充 -->
      </div>
      <div class="text-right">
        <button id="closeVersionInfoModalBottom" class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">关闭</button>
      </div>
    </div>
  </div>

  <footer class="mt-12 text-center text-gray-500 text-sm pb-6">
    <p>© 2025 阅读记录 | 记录每一本好书的阅读历程</p>
    <button id="versionInfoBtn" class="text-sm text-gray-500 hover:text-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">v0.0.6 20250715</button>
  </footer>

  <script>
    // API配置信息
    const API_CONFIG = {
      token: 'uskcZUvxWXvLIPXN0hUC6DK',
      datasheetId: 'dsthg9PyzRB9ABCVbN',
      viewId: 'viwyftA9tfZLb'
    };

    // 全局变量
    let allBooks = [];
    let filteredBooks = [];
    let currentPage = 1;
    const booksPerPage = 10;

    /**
     * @constant {string} CACHE_KEY - 缓存书籍数据的键名。
     */
    const CACHE_KEY = 'booksCache';
    /**
     * @constant {number} CACHE_TTL - 数据缓存的有效期，单位为毫秒（24小时）。
     */
    const CACHE_TTL = 24 * 60 * 60 * 1000; // 数据缓存有效期为 24 小时

    /**
     * 检查本地缓存是否有效。
     * 缓存的有效性基于时间戳和预设的有效期 `CACHE_TTL`。
     * @returns {boolean} 如果缓存有效则返回 `true`，否则返回 `false`。
     */
    function isCacheValid() {
      const cacheTimestamp = localStorage.getItem(`${CACHE_KEY}_timestamp`);
      if (!cacheTimestamp) return false;
      return Date.now() - parseInt(cacheTimestamp) < CACHE_TTL;
    }

    /**
     * 尝试从本地缓存加载书籍数据。
     * 如果缓存存在且有效，则解析缓存数据并更新全局书籍列表、统计信息和UI。
     * @returns {boolean} 如果成功从缓存加载数据则返回 `true`，否则返回 `false`。
     */
    function loadBooksFromCache() {
      const cachedData = localStorage.getItem(CACHE_KEY);
      if (cachedData && isCacheValid()) {
        allBooks = JSON.parse(cachedData);
        filteredBooks = [...allBooks];
        updateStatistics();
        renderBooks();
        loadAutocompleteData(); // 加载自动补全数据
        hideLoading();
        return true;
      }
      return false;
    }

    /**
     * 将书籍数据保存到本地缓存。
     * 同时更新缓存的时间戳。
     * @param {Array<Object>} books - 要保存的书籍数据数组。
     */
    function saveBooksToCache(books) {
      localStorage.setItem(CACHE_KEY, JSON.stringify(books));
      localStorage.setItem(`${CACHE_KEY}_timestamp`, Date.now().toString());
    }

    /**
     * 清除本地存储中过期的书籍缓存。
     * 如果缓存时间戳存在但已过期，则移除缓存数据和时间戳。
     */
    function clearExpiredCache() {
      const cacheTimestamp = localStorage.getItem(`${CACHE_KEY}_timestamp`);
      if (cacheTimestamp && !isCacheValid()) {
        localStorage.removeItem(CACHE_KEY);
        localStorage.removeItem(`${CACHE_KEY}_timestamp`);
      }
    }

    /**
     * 手动刷新书籍数据缓存。
     * 清除现有缓存，然后重新加载书籍数据并显示加载状态。
     */
    function refreshCache() {
      localStorage.removeItem(CACHE_KEY);
      localStorage.removeItem(`${CACHE_KEY}_timestamp`);
      loadBooks();
      showLoading();
      document.getElementById('cache-status').textContent = '（正在从云端加载最新数据）';
    }

    /**
     * 显示当前缓存状态到页面上。
     * 根据缓存的有效性显示缓存更新时间或提示当前使用的是云端数据。
     */
    function showCacheStatus() {
      const cacheTimestamp = localStorage.getItem(`${CACHE_KEY}_timestamp`);
      if (cacheTimestamp && isCacheValid()) {
        const lastUpdated = new Date(parseInt(cacheTimestamp)).toLocaleString();
        document.getElementById('cache-status').textContent = `（缓存更新时间：${lastUpdated}）`;
      } else {
        document.getElementById('cache-status').textContent = '（当前使用的是云端数据）';
      }
    }

    // 加载书籍数据
    async function loadBooks() {
      try {
        showLoading();
        clearExpiredCache(); // 清理过期缓存

        // 尝试从缓存加载数据
        if (loadBooksFromCache()) {
          console.log('使用缓存数据');
          return;
        }

        allBooks = [];

        const { token, datasheetId, viewId } = API_CONFIG;
        let pageToken = null;
        let hasMore = true;

        // 循环获取所有数据
        while (hasMore) {
          let url = `https://api.vika.cn/fusion/v1/datasheets/${datasheetId}/records?pageSize=1000`;
          if (viewId) url += `&viewId=${viewId}`;
          if (pageToken) url += `&pageToken=${pageToken}`;

          const response = await fetch(url, {
            method: 'GET',
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            }
          });

          if (response.ok) {
            const result = await response.json();
            const records = result.data?.records || [];
            allBooks = allBooks.concat(records);

            // 检查是否还有更多数据
            pageToken = result.data?.pageToken;
            hasMore = !!pageToken;
          } else {
            throw new Error('加载数据失败');
          }
        }

        // 更新缓存
        saveBooksToCache(allBooks);

        filteredBooks = [...allBooks];
        updateStatistics();
        renderBooks();
        await loadAutocompleteData(); // 加载自动补全数据
        hideLoading();
      } catch (error) {
        console.error('加载书籍失败:', error);
        hideLoading();
        showEmptyState();
      }
    }

    // 自动补全数据
    let autocompleteData = {
      book_names: [],
      authors: [],
      types: [],
      publishers: [],
      tags: []
    };

    // 自动补全状态
    let currentSuggestionIndex = -1;
    let currentSuggestions = [];

    // 渠道映射
    const channelMap = {
      '1': { name: '微信读书', color: 'bg-green-100 text-green-800' },
      '2': { name: 'Readest', color: 'bg-blue-100 text-blue-800' },
      '3': { name: 'Kindle', color: 'bg-orange-100 text-orange-800' },
      '4': { name: '实体书', color: 'bg-purple-100 text-purple-800' },
      '5': { name: '图书馆', color: 'bg-indigo-100 text-indigo-800' },
      '6': { name: 'iPad', color: 'bg-gray-100 text-gray-800' },
      '7': { name: '番茄', color: 'bg-gray-100 text-gray-800' }
    };

    // DOM元素
    const loadingState = document.getElementById('loading-state');
    const emptyState = document.getElementById('empty-state');
    const booksContainer = document.getElementById('books-container');
    const tableHeader = document.getElementById('table-header');
    const searchInput = document.getElementById('search-input');
    const statusFilter = document.getElementById('status-filter');
    const channelFilter = document.getElementById('channel-filter');
    const clearFiltersBtn = document.getElementById('clear-filters');
    const pagination = document.getElementById('pagination');
    const confirmModal = document.getElementById('confirm-modal');
    const loadingOverlay = document.getElementById('loading-overlay');

    // 设置事件监听器
    function setupEventListeners() {
      // 搜索功能
      searchInput.addEventListener('input', debounce(filterBooks, 300));

      // 筛选功能
      statusFilter.addEventListener('change', filterBooks);
      channelFilter.addEventListener('change', filterBooks);

      // 清除筛选
      clearFiltersBtn.addEventListener('click', clearFilters);

      // 确认对话框
      document.getElementById('confirm-cancel').addEventListener('click', hideConfirmModal);
      document.getElementById('confirm-ok').addEventListener('click', executeConfirmedAction);

      // 添加书籍模态窗口
      document.getElementById('add-book-btn').addEventListener('click', showAddBookModal);
      document.getElementById('close-add-modal').addEventListener('click', hideAddBookModal);
      document.getElementById('cancel-add-book').addEventListener('click', hideAddBookModal);
      document.getElementById('add-book-form').addEventListener('submit', handleAddBookSubmit);

      // 模态窗口外部点击关闭
      document.getElementById('add-book-modal').addEventListener('click', function(e) {
        if (e.target === this) {
          hideAddBookModal();
        }
      });
    }

    // 防抖函数
    function debounce(func, wait) {
      let timeout;
      return function executedFunction(...args) {
        const later = () => {
          clearTimeout(timeout);
          func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
      };
    }

    // 筛选书籍
    function filterBooks() {
      const searchTerm = searchInput.value.toLowerCase().trim();
      const statusValue = statusFilter.value;
      const channelValue = channelFilter.value;

      filteredBooks = allBooks.filter(book => {
        const fields = book.fields;

        // 搜索匹配
        const matchesSearch = !searchTerm ||
          (fields.book_name && fields.book_name.toLowerCase().includes(searchTerm)) ||
          (fields.author && fields.author.toLowerCase().includes(searchTerm));

        // 状态匹配
        const matchesStatus = !statusValue || fields.status === statusValue;

        // 渠道匹配
        const matchesChannel = !channelValue || fields.channel === channelValue;

        return matchesSearch && matchesStatus && matchesChannel;
      });

      currentPage = 1;
      renderBooks();
      updateStatistics();
    }

    // 清除筛选
    function clearFilters() {
      searchInput.value = '';
      statusFilter.value = '';
      channelFilter.value = '';
      filteredBooks = [...allBooks];
      currentPage = 1;
      renderBooks();
      updateStatistics();
    }

    // 更新统计信息
    function updateStatistics() {
      const total = allBooks.length;
      const completed = allBooks.filter(book => book.fields.status === '1').length;
      const reading = allBooks.filter(book => book.fields.status === '0').length;
      const abandoned = allBooks.filter(book => book.fields.status === '2').length;

      // 计算本月完成的书籍
      const currentMonth = new Date().getMonth();
      const currentYear = new Date().getFullYear();
      const monthly = allBooks.filter(book => {
        if (book.fields.status !== '1' || !book.fields.end_time) return false;
        const endDate = new Date(parseInt(book.fields.end_time) * 1000);
        return endDate.getMonth() === currentMonth && endDate.getFullYear() === currentYear;
      }).length;

      // 计算总字数（单位：万字）
      let totalWordCount = 0;
      allBooks.forEach(book => {
        const wordCount = parseFloat(book.fields.number_font);
        if (!isNaN(wordCount)) {
          totalWordCount += wordCount;
        }
      });

      document.getElementById('total-books').textContent = total;
      document.getElementById('completed-books').textContent = completed;
      document.getElementById('reading-books').textContent = reading;
      document.getElementById('abandoned-books').textContent = abandoned;
      document.getElementById('monthly-books').textContent = monthly;
      document.getElementById('books-count').textContent = `(${filteredBooks.length} 本书)`;
      document.getElementById('total-word-count').textContent = totalWordCount.toFixed(1);
    }

    // 渲染书籍列表
    function renderBooks() {
      if (filteredBooks.length === 0) {
        showEmptyState();
        return;
      }

      hideEmptyState();

      const startIndex = (currentPage - 1) * booksPerPage;
      const endIndex = startIndex + booksPerPage;
      const booksToShow = filteredBooks.slice(startIndex, endIndex);

      booksContainer.innerHTML = booksToShow.map((book, index) => createBookCard(book, startIndex + index)).join('');
      renderPagination();
    }

    // 全局变量用于生成数字ID
    let bookIdCounter = 1;

    // 创建书籍卡片
    function createBookCard(book, index) {
      const fields = book.fields;
      const recordId = book.recordId;
      const numericId = index + 1; // 使用索引+1作为数字ID

      // 格式化时间
      const formatTime = (timestamp) => {
        if (!timestamp) return '-';
        const date = new Date(parseInt(timestamp) * 1000);
        return date.toLocaleDateString('zh-CN');
      };

      // 计算用时天数
      const calculateDays = (startTime, endTime) => {
        if (!startTime || !endTime) return '-';
        const start = new Date(parseInt(startTime) * 1000);
        const end = new Date(parseInt(endTime) * 1000);
        const diffTime = Math.abs(end - start);
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        return `${diffDays}天`;
      };

      // 状态标签
      let statusBadge;
      if (fields.status === '1') {
        statusBadge = '<span class="status-badge bg-success text-white">看完</span>';
      } else if (fields.status === '2') {
        statusBadge = '<span class="status-badge bg-danger text-white">弃了</span>';
      } else {
        statusBadge = '<span class="status-badge bg-warning text-white">在看</span>';
      }

      // 渠道标签
      const channel = channelMap[fields.channel] || { name: '未知', color: 'bg-gray-100 text-gray-800' };
      const channelBadge = `<span class="channel-badge ${channel.color}">${channel.name}</span>`;

      // 格式化字数
      const formatWordCount = (count) => {
        if (!count) return '-';
        return `${count}万字`;
      };

      // 生成豆瓣链接的书名
      const bookNameWithLink = fields.book_name 
        ? `<a href="https://www.douban.com/search?q=${encodeURIComponent(fields.book_name)}" target="_blank" class="text-blue-600 hover:text-blue-800 hover:underline">${fields.book_name}</a>`
        : '-';

      // 操作按钮
      let actionButtons = '';
      if (fields.status === '0') {
        // 在看状态：显示完成和弃了按钮
        actionButtons = `
          <button onclick="finishBook('${recordId}', '${fields.book_name}')" class="px-3 py-1 bg-success text-white rounded text-sm btn-hover">
            <i class="fa fa-check mr-1"></i>完成
          </button>
          <button onclick="abandonBook('${recordId}', '${fields.book_name}')" class="px-3 py-1 bg-danger text-white rounded text-sm btn-hover">
            <i class="fa fa-times mr-1"></i>弃了
          </button>`;
      } else if (fields.status === '2') {
        // 弃了状态：可以重新开始阅读
        actionButtons = `
          <button onclick="restartBook('${recordId}', '${fields.book_name}')" class="px-3 py-1 bg-warning text-white rounded text-sm btn-hover">
            <i class="fa fa-refresh mr-1"></i>重新开始
          </button>`;
      }

      return `
        <div class="bg-gray-50 rounded-lg p-4 card-hover">
          <div class="grid grid-cols-1 lg:grid-cols-12 gap-4 items-center">
            <div class="lg:col-span-1 text-sm text-gray-500 font-mono">
              #${numericId}
            </div>
            <div class="lg:col-span-2">
              <h3 class="font-semibold text-gray-900 mb-1">${bookNameWithLink}</h3>
              <p class="text-sm text-gray-600">${fields.author || '-'}</p>
            </div>
            <div class="lg:col-span-1 text-sm text-gray-600">
              ${formatWordCount(fields.number_font)}
            </div>
            <div class="lg:col-span-1">
              ${statusBadge}
            </div>
            <div class="lg:col-span-1">
              ${channelBadge}
            </div>
            <div class="lg:col-span-1 text-sm text-gray-600">
              ${formatTime(fields.start_time)}
            </div>
            <div class="lg:col-span-1 text-sm text-gray-600">
              ${formatTime(fields.end_time)}
            </div>
            <div class="lg:col-span-1 text-sm text-gray-600">
              ${calculateDays(fields.start_time, fields.end_time)}
            </div>
            <div class="lg:col-span-1 text-sm text-gray-600">
              ${fields.remark || '-'}
            </div>
            <div class="lg:col-span-2 flex space-x-2">
              ${actionButtons}
            </div>
          </div>
        </div>
      `;
    }

    // 渲染分页
    function renderPagination() {
      const totalPages = Math.ceil(filteredBooks.length / booksPerPage);

      if (totalPages <= 1) {
        pagination.classList.add('hidden');
        return;
      }

      pagination.classList.remove('hidden');

      const prevBtn = document.getElementById('prev-page');
      const nextBtn = document.getElementById('next-page');
      const pageNumbers = document.getElementById('page-numbers');

      // 更新上一页/下一页按钮状态
      prevBtn.disabled = currentPage === 1;
      nextBtn.disabled = currentPage === totalPages;

      // 生成页码按钮
      pageNumbers.innerHTML = '';
      for (let i = 1; i <= totalPages; i++) {
        if (i === 1 || i === totalPages || (i >= currentPage - 2 && i <= currentPage + 2)) {
          const button = document.createElement('button');
          button.textContent = i;
          button.className = `px-3 py-2 rounded-lg border ${
            i === currentPage
              ? 'bg-primary text-white border-primary'
              : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
          }`;
          button.addEventListener('click', () => {
            currentPage = i;
            renderBooks();
          });
          pageNumbers.appendChild(button);
        } else if (i === currentPage - 3 || i === currentPage + 3) {
          const span = document.createElement('span');
          span.textContent = '...';
          span.className = 'px-3 py-2 text-gray-500';
          pageNumbers.appendChild(span);
        }
      }

      // 上一页/下一页事件
      prevBtn.onclick = () => {
        if (currentPage > 1) {
          currentPage--;
          renderBooks();
        }
      };

      nextBtn.onclick = () => {
        if (currentPage < totalPages) {
          currentPage++;
          renderBooks();
        }
      };
    }

    // 完成阅读
    async function finishBook(recordId, bookName) {
      showConfirmModal(
        `确定要将《${bookName}》标记为已完成吗？`,
        () => updateBookStatus(recordId, '1', Math.floor(Date.now() / 1000).toString())
      );
    }

    /**
     * 弃了书籍
     * @param {string} recordId - 书籍记录ID
     * @param {string} bookName - 书籍名称
     */
    async function abandonBook(recordId, bookName) {
      // 获取当前书籍的开始日期
      const book = allBooks.find(b => b.recordId === recordId);
      const startDate = book?.fields.start_time ? new Date(parseInt(book.fields.start_time) * 1000).toLocaleDateString() : '无';
      const currentDate = new Date().toLocaleDateString();
      
      // 获取原有备注内容
      const originalRemark = book?.fields.remark || '';
      
      // 构建新的备注内容，追加到原有备注后面
      const newRemark = `${originalRemark}${originalRemark ? '; ' : ''}开始日期: ${startDate}, 弃了日期: ${currentDate}`;
      
      showConfirmModal(
        `确定要将《${bookName}》标记为弃了吗？`,
        () => updateBookStatus(recordId, '2', Math.floor(Date.now() / 1000).toString(), null, newRemark)
      );
    }

    // 重新开始阅读
    async function restartBook(recordId, bookName) {
      showConfirmModal(
        `确定重新开始阅读《${bookName}》吗？`,
        () => updateBookStatus(recordId, '0', null, Math.floor(Date.now() / 1000).toString())
      );
    }

    /**
     * 更新书籍状态
     * @param {string} recordId - 书籍记录ID
     * @param {string} status - 书籍状态
     * @param {string} [endTime] - 结束时间戳
     * @param {string} [startTime] - 开始时间戳
     * @param {string} [remark] - 备注信息
     */
    async function updateBookStatus(recordId, status, endTime, startTime, remark) {
      try {
        showLoadingOverlay('正在更新状态...');

        const { token, datasheetId } = API_CONFIG;
        const url = `https://api.vika.cn/fusion/v1/datasheets/${datasheetId}/records`;

        const fields = {
          status: status
        };
        
        // 如果有备注信息则添加到更新字段
        if (remark !== undefined) {
          fields.remark = remark;
        }
        
        // 根据状态设置相应的时间字段
        if (endTime !== undefined) {
          fields.end_time = endTime;
        }
        if (startTime !== undefined) {
          fields.start_time = startTime;
          // 重新开始时清除结束时间
          if (status === '0') {
            fields.end_time = null;
          }
        }

        const response = await fetch(url, {
          method: 'PATCH',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            records: [{
              recordId: recordId,
              fields: fields
            }]
          })
        });

        if (response.ok) {
          await loadBooks(); // 重新加载数据
          hideLoadingOverlay();
          showSuccessMessage('状态更新成功！');
        } else {
          throw new Error('更新失败');
        }
      } catch (error) {
        console.error('更新状态失败:', error);
        hideLoadingOverlay();
        showErrorMessage('更新失败，请重试');
      }
    }

    // 显示/隐藏状态函数
    function showLoading() {
      loadingState.classList.remove('hidden');
      booksContainer.classList.add('hidden');
      emptyState.classList.add('hidden');
    }

    function hideLoading() {
      loadingState.classList.add('hidden');
    }

    function showEmptyState() {
      emptyState.classList.remove('hidden');
      booksContainer.classList.add('hidden');
      tableHeader.classList.add('hidden');
      pagination.classList.add('hidden');
    }

    function hideEmptyState() {
      emptyState.classList.add('hidden');
      booksContainer.classList.remove('hidden');
      tableHeader.classList.remove('hidden');
    }

    function showLoadingOverlay(message) {
      document.getElementById('loading-message').textContent = message;
      loadingOverlay.classList.remove('hidden');
    }

    function hideLoadingOverlay() {
      loadingOverlay.classList.add('hidden');
    }

    function showConfirmModal(message, onConfirm) {
      document.getElementById('confirm-message').textContent = message;
      confirmModal.classList.remove('hidden');

      // 存储确认回调
      window.pendingConfirmAction = onConfirm;
    }

    function hideConfirmModal() {
      confirmModal.classList.add('hidden');
      window.pendingConfirmAction = null;
    }

    function executeConfirmedAction() {
      if (window.pendingConfirmAction) {
        window.pendingConfirmAction();
        hideConfirmModal();
      }
    }

    // 消息提示函数
    function showSuccessMessage(message) {
      // 简单的成功提示，可以后续改为更美观的toast
      alert('✅ ' + message);
    }

    function showErrorMessage(message) {
      // 简单的错误提示，可以后续改为更美观的toast
      alert('❌ ' + message);
    }

    // 显示添加书籍模态窗口
    function showAddBookModal() {
      document.getElementById('add-book-modal').classList.remove('hidden');
      // 重置表单
      document.getElementById('add-book-form').reset();
      // 自动补全已在init时初始化，无需重复初始化
    }

    // 隐藏添加书籍模态窗口
    function hideAddBookModal() {
      document.getElementById('add-book-modal').classList.add('hidden');
    }

    // 处理添加书籍表单提交
    async function handleAddBookSubmit(e) {
      e.preventDefault();
      
      const formData = new FormData(e.target);
      const currentTimestamp = Math.floor(Date.now() / 1000).toString();
      
      const recordData = {
        user_id: ['recv0qBCrgcMw'], // 固定用户ID
        book_name: formData.get('book_name'),
        author: formData.get('author'),
        status: '0', // 保存记录自动设为在看状态
        type: formData.get('type'),
        channel: formData.get('channel'),
        number_font: formData.get('number_font') || undefined,
        remark: formData.get('remark') || undefined,
        start_time: currentTimestamp,
        create_time: currentTimestamp
      };
      
      // 过滤掉未定义的字段
      const filteredData = Object.fromEntries(
        Object.entries(recordData).filter(([_, value]) => value !== undefined)
      );
      
      try {
        showLoadingOverlay('正在保存阅读记录...');
        
        const { token, datasheetId, viewId } = API_CONFIG;
        const url = `https://api.vika.cn/fusion/v1/datasheets/${datasheetId}/records${viewId ? `?viewId=${viewId}` : ''}`;
        
        const response = await fetch(url, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            records: [{ fields: filteredData }],
            fieldKey: 'name'
          })
        });
        
        if (response.ok) {
          hideAddBookModal();
          await loadBooks(); // 重新加载数据
          hideLoadingOverlay();
          showSuccessMessage('书籍添加成功！');
        } else {
          throw new Error('添加失败');
        }
      } catch (error) {
        console.error('添加书籍失败:', error);
        hideLoadingOverlay();
        showErrorMessage('添加失败，请重试');
      }
    }

    // 加载自动完成数据
    async function loadAutocompleteData() {
      try {
        // 从现有书籍数据中提取唯一值
        const bookNames = [...new Set(allBooks.map(r => r.fields.book_name).filter(Boolean))];
        const authors = [...new Set(allBooks.map(r => r.fields.author).filter(Boolean))];
        const types = [...new Set(allBooks.map(r => r.fields.type).filter(Boolean))];

        autocompleteData = {
          book_names: bookNames,
          authors: authors,
          types: types
        };
      } catch (error) {
        console.error('加载自动完成数据失败:', error);
      }
    }





    /**
     * @description 设置单个自动补全输入框的逻辑。
     * @param {string} inputId - 输入框的ID。
     * @param {string} suggestionsId - 建议列表容器的ID。
     * @param {string} dataKey - 自动补全数据在 `autocompleteData` 对象中的键名。
     */
    function setupAutocompleteInput(inputId, suggestionsId, dataKey) {
      const input = document.getElementById(inputId);
      const suggestions = document.getElementById(suggestionsId);

      input.addEventListener('input', () => {
        const value = input.value.toLowerCase();
        if (!value) {
          suggestions.innerHTML = '';
          suggestions.classList.add('hidden');
          return;
        }

        const filtered = autocompleteData[dataKey].filter(item => 
          item.toLowerCase().includes(value)
        );

        if (filtered.length === 0) {
          suggestions.innerHTML = `<div class="py-2 px-4 text-gray-500">未找到匹配项</div>`;
          suggestions.classList.remove('hidden');
          return;
        }

        suggestions.innerHTML = filtered.map(item => 
          `<div class="py-2 px-4 hover:bg-gray-100 cursor-pointer" onclick="selectSuggestion(this, '${inputId}', '${suggestionsId}')">${item}</div>`
        ).join('');

        suggestions.classList.remove('hidden');
      });

      input.addEventListener('blur', () => {
        setTimeout(() => {
          suggestions.classList.add('hidden');
        }, 200);
      });
    }

    /**
     * @description 选择自动补全建议项
     * @param {HTMLElement} element - 被点击的建议元素
     * @param {string} inputId - 输入框ID
     * @param {string} suggestionsId - 建议容器ID
     */
    function selectSuggestion(element, inputId, suggestionsId) {
      const input = document.getElementById(inputId);
      const suggestions = document.getElementById(suggestionsId);

      input.value = element.textContent;
      suggestions.classList.add('hidden');

      // 触发input事件以便其他功能响应
      input.dispatchEvent(new Event('input', { bubbles: true }));
    }

    /**
     * @description 初始化页面中所有需要自动补全功能的输入框。
     *              包括书名、作者和类型。
     */
    function setupAutocomplete() {
      // 初始化自动补全输入框
      setupAutocompleteInput('modal_book_name', 'modal_book_name_suggestions', 'book_names');
      setupAutocompleteInput('modal_author', 'modal_author_suggestions', 'authors');
      setupAutocompleteInput('modal_type', 'modal_type_suggestions', 'types');
    }
    
    // 初始化函数
    async function init() {
      setupEventListeners();
      setupAutocomplete();
      await loadBooks();
      showCacheStatus();

      // 将“阅读”标题设置为强制刷新按钮
      const refreshButton = document.getElementById('refreshButton');
      if (refreshButton) {
          refreshButton.addEventListener('click', () => {
              refreshCache(true); // 强制刷新缓存
              showToast('缓存已强制刷新！', 'success');
          });
      }
    }

    // 页面加载时初始化
    window.addEventListener('load', function() {
        init();

        // 版本信息模态框
        const versionInfoModal = document.getElementById('versionInfoModal');
        const versionInfoBtn = document.getElementById('versionInfoBtn');
        const closeVersionInfoModalBtn = document.getElementById('closeVersionInfoModal');
        const versionContent = document.getElementById('versionContent');

        if (versionInfoBtn) {
            versionInfoBtn.addEventListener('click', () => {
                versionContent.innerHTML = `
                    <h3 class="text-lg font-bold mb-2">v0.0.6 20250715 版本更新</h3>
                    <ul class="list-disc list-inside text-gray-700">
                        <li>**修改**：弃了按钮备注逻辑优化，保留原有备注并追加新的信息。</li>
                        <li>**优化**：页面顶部“阅读”标题现已作为强制刷新按钮，点击可刷新缓存。</li>
                        <li>**新增**：版本信息按钮及模态框，用于展示版本更新记录。</li>
                    </ul>
                `;
                versionInfoModal.classList.remove('hidden');
            });
        }

        if (closeVersionInfoModalBtn) {
            closeVersionInfoModalBtn.addEventListener('click', () => {
                versionInfoModal.classList.add('hidden');
            });
        }

        // 点击模态框外部关闭
        if (versionInfoModal) {
            versionInfoModal.addEventListener('click', (e) => {
                if (e.target === versionInfoModal) {
                    versionInfoModal.classList.add('hidden');
                }
            });
        }

        // 为底部的关闭按钮添加事件监听器
        const closeVersionInfoModalBottomBtn = document.getElementById('closeVersionInfoModalBottom');
        if (closeVersionInfoModalBottomBtn) {
            closeVersionInfoModalBottomBtn.addEventListener('click', () => {
                versionInfoModal.classList.add('hidden');
            });
        }
    });



  </script>


</body>
</html>
