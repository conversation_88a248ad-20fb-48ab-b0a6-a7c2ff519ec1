<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>书籍列表 - 阅读记录</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link href="https://cdn.bootcdn.net/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css" rel="stylesheet">
  <!-- 备用图标样式，如果Font Awesome加载失败 -->
  <style>
    .fa-fallback {
      display: inline-block;
      width: 1em;
      text-align: center;
    }
    .fa-book::before { content: "📚"; }
    .fa-plus::before { content: "➕"; }
    .fa-edit::before { content: "✏️"; }
    .fa-trash::before { content: "🗑️"; }
    .fa-check-circle::before { content: "✅"; }
    .fa-clock-o::before { content: "⏰"; }
    .fa-line-chart::before { content: "📈"; }
    .fa-info-circle::before { content: "ℹ️"; }
    .fa-exclamation-triangle::before { content: "⚠️"; }
  </style>
  <script charset="UTF-8" id="LA_COLLECT" src="//sdk.51.la/js-sdk-pro.min.js"></script>
  <script>
    // 安全地初始化51.la统计
    if (typeof LA !== 'undefined') {
      LA.init({id:"Jqud4Iv4jX9nBtrP",ck:"Jqud4Iv4jX9nBtrP"});
    } else {
      console.warn('51.la SDK 加载失败，统计功能不可用');
    }
  </script>

  <!-- Tailwind 配置 -->
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: '#3B82F6',
            secondary: '#6366F1',
            success: '#10B981',
            danger: '#EF4444',
            warning: '#F59E0B',
            info: '#06B6D4',
            light: '#F8FAFC',
            dark: '#1F2937'
          },
          fontFamily: {
            inter: ['Inter', 'system-ui', 'sans-serif']
          }
        }
      }
    }
  </script>

  <style type="text/tailwindcss">
    @layer utilities {
      .content-auto {
        content-visibility: auto;
      }
      .card-shadow {
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
      }
      .card-hover {
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        transform: translateY(-1px);
        transition: all 300ms ease;
      }
      .btn-hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.12);
        transition: all 300ms ease;
      }
      .status-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.75rem;
        line-height: 1rem;
        font-weight: 500;
      }
      .channel-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        font-size: 0.75rem;
        line-height: 1rem;
        font-weight: 500;
      }
    }
  </style>
</head>
<body class="bg-gradient-to-br from-blue-50 to-indigo-50 min-h-screen font-inter text-dark">
  <div class="container mx-auto px-4 py-8 max-w-7xl">
    <!-- 头部区域 -->
    <header class="mb-8">
      <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h1 id="refreshButton" class="text-3xl font-bold text-gray-800 cursor-pointer hover:text-primary transition-colors duration-300">阅读</h1>
          <p class="text-gray-600 mt-1">记录您的阅读历程 <span id="cache-status" class="text-sm text-gray-500"></span></p>
        </div>
        <div class="flex items-center gap-4">
          <button id="versionInfoBtn" class="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors duration-300">
            <i class="fa fa-info-circle mr-2"></i>版本信息
          </button>
          <button id="add-book-btn" class="inline-flex items-center px-6 py-3 bg-primary text-white rounded-lg btn-hover">
            <i class="fa fa-plus mr-2"></i>添加书籍
          </button>
        </div>
      </div>
    </header>

    <!-- 统计信息 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <div class="bg-white p-6 rounded-xl card-shadow">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-primary/10 text-primary">
            <i class="fa fa-book text-xl"></i>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">总书籍</p>
            <p class="text-2xl font-semibold text-gray-900" id="total-books">0</p>
          </div>
        </div>
      </div>
      <div class="bg-white p-6 rounded-xl card-shadow">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-success/10 text-success">
            <i class="fa fa-check-circle text-xl"></i>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">已完成</p>
            <p class="text-2xl font-semibold text-gray-900" id="completed-books">0</p>
          </div>
        </div>
      </div>
      <div class="bg-white p-6 rounded-xl card-shadow">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-warning/10 text-warning">
            <i class="fa fa-clock-o text-xl"></i>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">在读</p>
            <p class="text-2xl font-semibold text-gray-900" id="reading-books">0</p>
          </div>
        </div>
      </div>
      <div class="bg-white p-6 rounded-xl card-shadow">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-info/10 text-info">
            <i class="fa fa-line-chart text-xl"></i>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">本月完成</p>
            <p class="text-2xl font-semibold text-gray-900" id="monthly-books">0</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="bg-white p-6 rounded-xl card-shadow mb-8">
      <div class="flex flex-col lg:flex-row gap-4">
        <div class="flex-1">
          <input type="text" id="search-input" placeholder="搜索书名、作者..."
                 class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
        </div>
        <div class="flex flex-col sm:flex-row gap-4">
          <select id="status-filter" class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
            <option value="">全部状态</option>
            <option value="0">在看</option>
            <option value="1">看完</option>
            <option value="2">弃了</option>
          </select>
          <select id="channel-filter" class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
            <option value="">全部渠道</option>
            <option value="1">微信读书</option>
            <option value="2">Readest</option>
            <option value="3">Kindle</option>
            <option value="4">实体书</option>
            <option value="5">图书馆</option>
            <option value="6">iPad</option>
            <option value="7">番茄</option>
          </select>
          <button id="clear-filters" class="px-6 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors duration-300">
            清除筛选
          </button>
        </div>
      </div>
    </div>

    <!-- 书籍列表标题 -->
    <div class="flex items-center justify-between mb-6">
      <h2 class="text-xl font-semibold text-gray-800">书籍列表 <span id="books-count" class="text-sm text-gray-500">(0 本书)</span></h2>
    </div>

    <!-- 加载状态 -->
    <div id="loading-state" class="hidden">
      <div class="text-center py-12">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        <p class="mt-4 text-gray-600">正在加载书籍...</p>
      </div>
    </div>

    <!-- 空状态 -->
    <div id="empty-state" class="hidden">
      <div class="text-center py-12">
        <div class="mx-auto h-24 w-24 text-gray-400">
          <i class="fa fa-book text-6xl"></i>
        </div>
        <h3 class="text-lg font-medium text-gray-900 mb-2">暂无书籍记录</h3>
        <p class="text-gray-600 mb-4">开始添加您的第一本书吧！</p>
        <button onclick="showAddBookModal()" class="inline-flex items-center px-4 py-2 bg-primary text-white rounded-lg btn-hover">
          <i class="fa fa-plus mr-2"></i>添加书籍
        </button>
      </div>
    </div>

    <!-- 书籍网格 -->
    <div id="books-container" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8">
      <!-- 书籍卡片将通过 JavaScript 动态生成 -->
    </div>

    <!-- 分页 -->
    <div id="pagination" class="flex justify-center">
      <!-- 分页按钮将通过 JavaScript 动态生成 -->
    </div>
  </div>

  <!-- 加载遮罩 -->
  <div id="loading-overlay" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white p-6 rounded-lg shadow-xl">
      <div class="flex items-center">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mr-4"></div>
        <span id="loading-message" class="text-gray-700">正在加载...</span>
      </div>
    </div>
  </div>

  <!-- 确认模态框 -->
  <div id="confirm-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden flex items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md mx-auto">
      <div class="flex items-center mb-4">
        <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
          <i class="fa fa-exclamation-triangle text-red-600"></i>
        </div>
      </div>
      <div class="text-center">
        <h3 class="text-lg font-medium text-gray-900 mb-2">确认操作</h3>
        <p id="confirm-message" class="text-sm text-gray-500 mb-4"></p>
        <div class="flex justify-center space-x-4">
          <button id="confirm-cancel" class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">取消</button>
          <button id="confirm-ok" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">确认</button>
        </div>
      </div>
    </div>
  </div>

  <!-- 添加/编辑书籍模态框 -->
  <div id="add-book-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden flex items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-2xl mx-auto max-h-screen overflow-y-auto">
      <div class="flex justify-between items-center mb-6">
        <h2 class="text-xl font-semibold text-gray-900">添加书籍</h2>
        <button id="close-add-modal" class="text-gray-500 hover:text-gray-700 focus:outline-none">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>

      <form id="add-book-form" class="space-y-4">
        <input type="hidden" id="edit-book-id" value="">

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="relative">
            <label for="modal_book_name" class="block text-sm font-medium text-gray-700 mb-1">书名 *</label>
            <input type="text" id="modal_book_name" name="book_name" required
                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
            <div id="modal_book_name_suggestions" class="absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg hidden max-h-40 overflow-y-auto"></div>
          </div>

          <div class="relative">
            <label for="modal_author" class="block text-sm font-medium text-gray-700 mb-1">作者 *</label>
            <input type="text" id="modal_author" name="author" required
                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
            <div id="modal_author_suggestions" class="absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg hidden max-h-40 overflow-y-auto"></div>
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="relative">
            <label for="modal_type" class="block text-sm font-medium text-gray-700 mb-1">类型</label>
            <input type="text" id="modal_type" name="type"
                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
            <div id="modal_type_suggestions" class="absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg hidden max-h-40 overflow-y-auto"></div>
          </div>

          <div>
            <label for="modal_status" class="block text-sm font-medium text-gray-700 mb-1">状态 *</label>
            <select id="modal_status" name="status" required
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
              <option value="">请选择状态</option>
              <option value="0">在看</option>
              <option value="1">看完</option>
              <option value="2">弃了</option>
            </select>
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label for="modal_channel" class="block text-sm font-medium text-gray-700 mb-1">渠道</label>
            <select id="modal_channel" name="channel"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
              <option value="">请选择渠道</option>
              <option value="1">微信读书</option>
              <option value="2">Readest</option>
              <option value="3">Kindle</option>
              <option value="4">实体书</option>
              <option value="5">图书馆</option>
              <option value="6">iPad</option>
              <option value="7">番茄</option>
            </select>
          </div>

          <div>
            <label for="modal_word_count" class="block text-sm font-medium text-gray-700 mb-1">字数（万字）</label>
            <input type="number" id="modal_word_count" name="word_count" step="0.1" min="0"
                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label for="modal_start_time" class="block text-sm font-medium text-gray-700 mb-1">开始时间</label>
            <input type="date" id="modal_start_time" name="start_time"
                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
          </div>

          <div>
            <label for="modal_end_time" class="block text-sm font-medium text-gray-700 mb-1">结束时间</label>
            <input type="date" id="modal_end_time" name="end_time"
                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
          </div>
        </div>

        <div>
          <label for="modal_rating" class="block text-sm font-medium text-gray-700 mb-1">评分</label>
          <select id="modal_rating" name="rating"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
            <option value="">请选择评分</option>
            <option value="1">1分</option>
            <option value="2">2分</option>
            <option value="3">3分</option>
            <option value="4">4分</option>
            <option value="5">5分</option>
          </select>
        </div>

        <div>
          <label for="modal_comment" class="block text-sm font-medium text-gray-700 mb-1">备注</label>
          <textarea id="modal_comment" name="comment" rows="3"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    placeholder="请输入备注信息..."></textarea>
        </div>

        <div class="flex justify-end space-x-4 pt-4">
          <button type="button" id="cancel-add-book" class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">取消</button>
          <button type="submit" class="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">保存</button>
        </div>
      </form>
    </div>
  </div>

  <!-- 版本信息模态框 -->
  <div id="versionInfoModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden flex items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md mx-auto">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-semibold">版本信息</h2>
        <button id="closeVersionInfoModal" class="text-gray-500 hover:text-gray-700 focus:outline-none">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>
      <div id="versionContent" class="mb-4 text-gray-800">
        <!-- 版本内容将通过 JavaScript 填充 -->
      </div>
      <div class="text-right">
        <button id="closeVersionInfoModalBottom" class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">关闭</button>
      </div>
    </div>
  </div>

  <!-- Toast 容器 -->
  <div id="toast-container" class="fixed bottom-4 right-4 z-50 space-y-2"></div>

  <!-- JavaScript 代码 -->
  <script>
    // API 配置
    const API_CONFIG = {
      token: 'uskcZUvxWXvLIPXN0hUC6DK',
      datasheetId: 'dsthg9PyzRB9ABCVbN',
      viewId: 'viwyftA9tfZLb'
    };

    // 全局变量
    let allBooks = [];
    let filteredBooks = [];
    let currentPage = 1;
    const booksPerPage = 12;
    const CACHE_KEY = 'books_cache';
    const CACHE_TTL = 24 * 60 * 60 * 1000; // 24小时

    // DOM 元素
    let loadingState, emptyState, booksContainer, searchInput, statusFilter, channelFilter, clearFiltersBtn, pagination, confirmModal, loadingOverlay;
    let addBookModal, addBookForm;

    // 自动补全数据
    let autocompleteData = {
      book_names: [],
      authors: [],
      types: []
    };

    // 渠道映射
    const channelMap = {
      '1': { name: '微信读书', color: 'bg-green-100 text-green-800' },
      '2': { name: 'Readest', color: 'bg-blue-100 text-blue-800' },
      '3': { name: 'Kindle', color: 'bg-orange-100 text-orange-800' },
      '4': { name: '实体书', color: 'bg-purple-100 text-purple-800' },
      '5': { name: '图书馆', color: 'bg-indigo-100 text-indigo-800' },
      '6': { name: 'iPad', color: 'bg-gray-100 text-gray-800' },
      '7': { name: '番茄', color: 'bg-gray-100 text-gray-800' }
    };

    // 缓存相关函数
    function isCacheValid() {
      const cacheTimestamp = localStorage.getItem(`${CACHE_KEY}_timestamp`);
      if (!cacheTimestamp) return false;
      return Date.now() - parseInt(cacheTimestamp) < CACHE_TTL;
    }

    function loadBooksFromCache() {
      try {
        const cachedData = localStorage.getItem(CACHE_KEY);
        if (!cachedData || !isCacheValid()) {
          hideLoadingOverlay();
          showEmptyState();
          return false;
        }

        const books = JSON.parse(cachedData);
        allBooks = books.map(book => ({
          recordId: book.recordId,
          fields: book.fields,
          book_name: book.fields.book_name || '',
          author: book.fields.author || '',
          type: book.fields.type || '',
          status: book.fields.status || '',
          notes: book.fields.comment || ''
        }));

        filteredBooks = [...allBooks];
        loadAutocompleteData();
        updateStatistics();
        renderBooks();
        return true;
      } catch (error) {
        console.error('加载书籍失败:', error);
        showToast('加载书籍失败，请检查网络或API配置！', 'error');
      } finally {
        hideLoadingOverlay();
      }
      return false;
    }

    function saveBooksToCache(books) {
      localStorage.setItem(CACHE_KEY, JSON.stringify(books));
      localStorage.setItem(`${CACHE_KEY}_timestamp`, Date.now().toString());
    }

    function clearExpiredCache() {
      const cacheTimestamp = localStorage.getItem(`${CACHE_KEY}_timestamp`);
      if (cacheTimestamp && !isCacheValid()) {
        localStorage.removeItem(CACHE_KEY);
        localStorage.removeItem(`${CACHE_KEY}_timestamp`);
      }
    }

    function refreshCache(force = false) {
      if (force || !isCacheValid()) {
        localStorage.removeItem(CACHE_KEY);
        localStorage.removeItem(`${CACHE_KEY}_timestamp`);
        loadBooks();
        document.getElementById('cache-status').textContent = '（正在从云端加载最新数据）';
      } else {
        showToast('缓存数据仍然有效，无需刷新', 'info');
      }
    }

    function showCacheStatus() {
      const cacheTimestamp = localStorage.getItem(`${CACHE_KEY}_timestamp`);
      if (cacheTimestamp && isCacheValid()) {
        const lastUpdated = new Date(parseInt(cacheTimestamp)).toLocaleString();
        document.getElementById('cache-status').textContent = `（缓存更新时间：${lastUpdated}）`;
      } else {
        document.getElementById('cache-status').textContent = '（当前使用的是云端数据）';
      }
    }

    // 网络连接检查
    function isOnline() {
      return navigator.onLine;
    }

    // 加载书籍数据
    async function loadBooks() {
      try {
        showLoadingOverlay('正在加载书籍...');
        clearExpiredCache();

        // 检查网络连接
        if (!isOnline()) {
          console.log('网络不可用，尝试使用缓存数据');
          if (loadBooksFromCache()) {
            showToast('网络不可用，已加载缓存数据', 'warning');
            return;
          } else {
            throw new Error('网络不可用且无缓存数据');
          }
        }

        // 尝试从缓存加载数据
        if (loadBooksFromCache()) {
          console.log('使用缓存数据');
          showToast('已加载缓存数据', 'info');
          return;
        }

        const { token, datasheetId, viewId } = API_CONFIG;
        allBooks = [];
        let hasMore = true;
        let pageToken = null;

        // 循环获取所有数据
        while (hasMore) {
          let url = `https://api.vika.cn/fusion/v1/datasheets/${datasheetId}/records?pageSize=1000`;
          if (viewId) url += `&viewId=${viewId}`;
          if (pageToken) url += `&pageToken=${pageToken}`;

          const response = await fetch(url, {
            method: 'GET',
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            }
          });

          if (response.ok) {
            const result = await response.json();
            const records = result.data?.records || [];
            allBooks = allBooks.concat(records);

            hasMore = result.data?.pageToken ? true : false;
            pageToken = result.data?.pageToken;
            console.log('pageToken:', pageToken, 'hasMore:', hasMore);
          } else {
            console.error('API 请求失败，状态码:', response.status);
            throw new Error(`加载数据失败，状态码: ${response.status}`);
          }
        }

        // 保存到缓存
        saveBooksToCache(allBooks);

        // 处理数据
        allBooks = allBooks.map(book => ({
          recordId: book.recordId,
          fields: book.fields,
          book_name: book.fields.book_name || '',
          author: book.fields.author || '',
          type: book.fields.type || '',
          status: book.fields.status || '',
          notes: book.fields.comment || ''
        }));

        filteredBooks = [...allBooks];
        loadAutocompleteData();
        updateStatistics();
        renderBooks();

        showToast(`成功加载 ${allBooks.length} 本书籍`, 'success');
      } catch (error) {
        console.error('加载书籍失败:', error);
        showToast('加载书籍失败，请检查网络或API配置！', 'error');
      } finally {
        hideLoadingOverlay();
      }
      showEmptyState();
    }

    // 自动补全数据加载
    function loadAutocompleteData() {
      const bookNames = new Set();
      const authors = new Set();
      const types = new Set();

      allBooks.forEach(book => {
        if (book.fields.book_name) bookNames.add(book.fields.book_name);
        if (book.fields.author) authors.add(book.fields.author);
        if (book.fields.type) types.add(book.fields.type);
      });

      autocompleteData.book_names = Array.from(bookNames);
      autocompleteData.authors = Array.from(authors);
      autocompleteData.types = Array.from(types);
    }

    // UI 控制函数
    function showLoadingOverlay(message) {
      document.getElementById('loading-message').textContent = message;
      loadingOverlay.classList.remove('hidden');
    }

    function hideLoadingOverlay() {
      loadingOverlay.classList.add('hidden');
    }

    function showEmptyState() {
      emptyState.classList.remove('hidden');
      booksContainer.innerHTML = '';
      pagination.innerHTML = '';
    }

    function hideEmptyState() {
      emptyState.classList.add('hidden');
    }

    function showSuccessMessage(message) {
      alert('✅ ' + message);
    }

    function showErrorMessage(message) {
      alert('❌ ' + message);
    }

    function showToast(message, type = 'info') {
      const toastContainer = document.getElementById('toast-container');
      if (!toastContainer) {
        console.warn('Toast container not found.');
        return;
      }

      const toast = document.createElement('div');
      toast.className = 'px-4 py-3 rounded-lg shadow-lg text-white max-w-sm';

      switch (type) {
        case 'success':
          toast.classList.add('bg-green-500');
          break;
        case 'error':
          toast.classList.add('bg-red-500');
          break;
        case 'warning':
          toast.classList.add('bg-yellow-500');
          break;
        case 'info':
        default:
          toast.classList.add('bg-blue-500');
          break;
      }

      toast.textContent = message;
      toastContainer.appendChild(toast);

      setTimeout(() => {
        toast.remove();
      }, 3000);
    }

    // 模态框控制
    function showAddBookModal() {
      addBookModal.classList.remove('hidden');
      addBookForm.reset();
      document.getElementById('edit-book-id').value = '';
      initModalAutocomplete();
    }

    function hideAddBookModal() {
      addBookModal.classList.add('hidden');
      addBookForm.reset();
      document.getElementById('edit-book-id').value = '';
    }

    function showConfirmModal(message, onConfirm) {
      document.getElementById('confirm-message').textContent = message;
      confirmModal.classList.remove('hidden');
      window.pendingConfirmAction = onConfirm;
    }

    function hideConfirmModal() {
      confirmModal.classList.add('hidden');
      window.pendingConfirmAction = null;
    }

    function executeConfirmedAction() {
      if (window.pendingConfirmAction) {
        window.pendingConfirmAction();
        hideConfirmModal();
      }
    }

    // 工具函数
    function debounce(func, wait) {
      let timeout;
      return function executedFunction(...args) {
        const later = () => {
          clearTimeout(timeout);
          func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
      };
    }

    function getStatusBadgeClass(status) {
      switch (status) {
        case '0': return 'bg-warning/10 text-warning';
        case '1': return 'bg-success/10 text-success';
        case '2': return 'bg-danger/10 text-danger';
        default: return 'bg-gray-100 text-gray-800';
      }
    }

    function getStatusText(status) {
      switch (status) {
        case '0': return '在看';
        case '1': return '看完';
        case '2': return '弃了';
        default: return '未知';
      }
    }

    function getChannelInfo(channelId) {
      return channelMap[channelId] || { name: '未知', color: 'bg-gray-100 text-gray-800' };
    }

    function formatDate(timestamp) {
      if (!timestamp) return 'N/A';
      const date = new Date(timestamp * 1000);
      return date.toLocaleDateString('zh-CN', { year: 'numeric', month: '2-digit', day: '2-digit' });
    }

    function calculateDays(startTimestamp, endTimestamp) {
      if (!startTimestamp || !endTimestamp) return 'N/A';
      const startDate = new Date(startTimestamp * 1000);
      const endDate = new Date(endTimestamp * 1000);
      const diffTime = Math.abs(endDate - startDate);
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      return `${diffDays} 天`;
    }

    // 数据处理函数
    function filterBooks() {
      const searchTerm = searchInput.value.toLowerCase();
      const statusValue = statusFilter.value;
      const channelValue = channelFilter.value;

      filteredBooks = allBooks.filter(book => {
        const matchesSearch = !searchTerm ||
          book.book_name.toLowerCase().includes(searchTerm) ||
          book.author.toLowerCase().includes(searchTerm) ||
          book.type.toLowerCase().includes(searchTerm);

        const matchesStatus = !statusValue || book.status === statusValue;
        const matchesChannel = !channelValue || book.fields.channel === channelValue;

        return matchesSearch && matchesStatus && matchesChannel;
      });

      currentPage = 1;
      renderBooks();
      updateStatistics();
    }

    function clearFilters() {
      searchInput.value = '';
      statusFilter.value = '';
      channelFilter.value = '';
      filteredBooks = [...allBooks];
      currentPage = 1;
      renderBooks();
      updateStatistics();
    }

    function updateStatistics() {
      const total = allBooks.length;
      const completed = allBooks.filter(book => book.fields.status === '1').length;
      const reading = allBooks.filter(book => book.fields.status === '0').length;

      // 计算本月完成的书籍
      const currentDate = new Date();
      const currentMonth = currentDate.getMonth();
      const currentYear = currentDate.getFullYear();

      const monthly = allBooks.filter(book => {
        if (book.fields.status !== '1' || !book.fields.end_time) return false;
        const endDate = new Date(book.fields.end_time * 1000);
        return endDate.getMonth() === currentMonth && endDate.getFullYear() === currentYear;
      }).length;

      // 计算总字数
      let totalWordCount = 0;
      allBooks.forEach(book => {
        const wordCount = parseFloat(book.fields.number_font);
        if (!isNaN(wordCount)) {
          totalWordCount += wordCount;
        }
      });

      document.getElementById('total-books').textContent = total;
      document.getElementById('completed-books').textContent = completed;
      document.getElementById('reading-books').textContent = reading;
      document.getElementById('monthly-books').textContent = monthly;
      document.getElementById('books-count').textContent = `(${filteredBooks.length} 本书)`;
    }

    // 渲染函数
    function renderBooks() {
      booksContainer.innerHTML = '';

      if (filteredBooks.length === 0) {
        showEmptyState();
        renderPagination(0);
        return;
      }

      hideEmptyState();

      const startIndex = (currentPage - 1) * booksPerPage;
      const endIndex = startIndex + booksPerPage;
      const booksToRender = filteredBooks.slice(startIndex, endIndex);

      booksToRender.forEach(book => {
        const statusClass = getStatusBadgeClass(book.fields.status);
        const statusText = getStatusText(book.fields.status);
        const channelInfo = getChannelInfo(book.fields.channel);
        const startDate = formatDate(book.fields.start_time);
        const endDate = formatDate(book.fields.end_time);
        const days = calculateDays(book.fields.start_time, book.fields.end_time);
        const wordCount = book.fields.number_font ? `${book.fields.number_font}万字` : 'N/A';
        const rating = book.fields.rating ? `${book.fields.rating}分` : 'N/A';

        const bookCard = `
          <div class="bg-white p-6 rounded-xl card-shadow hover:card-hover transition-all duration-300">
            <div class="flex flex-col h-full">
              <div class="flex-1">
                <h3 class="text-lg font-semibold text-gray-800 mb-2 line-clamp-2">${book.book_name}</h3>
                <p class="text-gray-600 mb-1">作者: ${book.author}</p>
                <p class="text-gray-600 mb-1">类型: ${book.type || 'N/A'}</p>
                <p class="text-gray-600 mb-2">字数: ${wordCount}</p>

                <div class="flex flex-wrap gap-2 mb-3">
                  <span class="status-badge ${statusClass}">${statusText}</span>
                  ${book.fields.channel ? `<span class="channel-badge ${channelInfo.color}">${channelInfo.name}</span>` : ''}
                  ${book.fields.rating ? `<span class="channel-badge bg-yellow-100 text-yellow-800">⭐ ${rating}</span>` : ''}
                </div>

                <div class="text-sm text-gray-500 mb-3">
                  <p>开始: ${startDate}</p>
                  <p>结束: ${endDate}</p>
                  ${book.fields.start_time && book.fields.end_time ? `<p>用时: ${days}</p>` : ''}
                </div>

                ${book.notes ? `<p class="text-sm text-gray-600 mb-3 line-clamp-2">${book.notes}</p>` : ''}
              </div>

              <div class="flex space-x-2 pt-3 border-t border-gray-100">
                <button onclick="editBook('${book.recordId}')" class="flex-1 px-3 py-2 bg-blue-500 text-white text-sm rounded-md hover:bg-blue-600 transition-colors duration-300">
                  <i class="fa fa-edit mr-1"></i>编辑
                </button>
                <button onclick="deleteBook('${book.recordId}')" class="flex-1 px-3 py-2 bg-red-500 text-white text-sm rounded-md hover:bg-red-600 transition-colors duration-300">
                  <i class="fa fa-trash mr-1"></i>删除
                </button>
              </div>
            </div>
          </div>
        `;
        booksContainer.innerHTML += bookCard;
      });

      renderPagination(filteredBooks.length);
    }

    function renderPagination(totalBooks) {
      const totalPages = Math.ceil(totalBooks / booksPerPage);
      pagination.innerHTML = '';

      if (totalPages <= 1) {
        return;
      }

      const ul = document.createElement('ul');
      ul.className = 'flex items-center space-x-2';

      // Previous button
      const prevLi = document.createElement('li');
      const prevBtn = document.createElement('button');
      prevBtn.textContent = '上一页';
      prevBtn.className = `px-3 py-1 rounded-lg ${currentPage === 1 ? 'bg-gray-200 text-gray-500 cursor-not-allowed' : 'bg-primary text-white hover:bg-primary/90'}`;
      prevBtn.disabled = currentPage === 1;
      prevBtn.onclick = () => {
        if (currentPage > 1) {
          currentPage--;
          renderBooks();
        }
      };
      prevLi.appendChild(prevBtn);
      ul.appendChild(prevLi);

      // Page numbers
      for (let i = 1; i <= totalPages; i++) {
        const li = document.createElement('li');
        const btn = document.createElement('button');
        btn.className = `px-3 py-1 rounded-lg ${i === currentPage ? 'bg-primary text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`;
        btn.textContent = i;
        btn.onclick = () => {
          currentPage = i;
          renderBooks();
        };
        li.appendChild(btn);
        ul.appendChild(li);
      }

      // Next button
      const nextLi = document.createElement('li');
      const nextBtn = document.createElement('button');
      nextBtn.textContent = '下一页';
      nextBtn.className = `px-3 py-1 rounded-lg ${currentPage === totalPages ? 'bg-gray-200 text-gray-500 cursor-not-allowed' : 'bg-primary text-white hover:bg-primary/90'}`;
      nextBtn.disabled = currentPage === totalPages;
      nextBtn.onclick = () => {
        if (currentPage < totalPages) {
          currentPage++;
          renderBooks();
        }
      };
      nextLi.appendChild(nextBtn);
      ul.appendChild(nextLi);

      pagination.appendChild(ul);
    }

    // 编辑和删除函数
    function editBook(recordId) {
      const bookToEdit = allBooks.find(book => book.recordId === recordId);
      if (!bookToEdit) {
        showErrorMessage('未找到要编辑的书籍。');
        return;
      }

      const fields = bookToEdit.fields;
      document.getElementById('edit-book-id').value = recordId;
      document.getElementById('modal_book_name').value = fields.book_name || '';
      document.getElementById('modal_author').value = fields.author || '';
      document.getElementById('modal_type').value = fields.type || '';
      document.getElementById('modal_status').value = fields.status || '';
      document.getElementById('modal_channel').value = fields.channel || '';
      document.getElementById('modal_word_count').value = fields.number_font || '';
      document.getElementById('modal_rating').value = fields.rating || '';
      document.getElementById('modal_comment').value = fields.comment || '';

      // 处理日期字段
      if (fields.start_time) {
        const startDate = new Date(fields.start_time * 1000);
        document.getElementById('modal_start_time').value = startDate.toISOString().split('T')[0];
      }
      if (fields.end_time) {
        const endDate = new Date(fields.end_time * 1000);
        document.getElementById('modal_end_time').value = endDate.toISOString().split('T')[0];
      }

      showAddBookModal();
    }

    function deleteBook(recordId) {
      showConfirmModal('确定要删除这本书籍吗？此操作不可撤销。', async () => {
        showLoadingOverlay('正在删除书籍...');
        const { token, datasheetId } = API_CONFIG;
        try {
          const response = await fetch(`https://api.vika.cn/fusion/v1/datasheets/${datasheetId}/records/${recordId}`, {
            method: 'DELETE',
            headers: {
              'Authorization': `Bearer ${token}`
            }
          });

          if (response.ok) {
            showSuccessMessage('书籍删除成功！');
            await loadBooks();
          } else {
            const errorData = await response.json();
            throw new Error(`API错误: ${errorData.message || response.statusText}`);
          }
        } catch (error) {
          console.error('删除书籍失败:', error);
          showErrorMessage(`删除书籍失败: ${error.message}`);
        } finally {
          hideLoadingOverlay();
        }
      });
    }

    // 表单提交处理
    async function handleAddBookSubmit(event) {
      event.preventDefault();

      const bookId = document.getElementById('edit-book-id').value;
      const bookName = document.getElementById('modal_book_name').value;
      const author = document.getElementById('modal_author').value;
      const type = document.getElementById('modal_type').value;
      const status = document.getElementById('modal_status').value;
      const channel = document.getElementById('modal_channel').value;
      const startDate = document.getElementById('modal_start_time').value;
      const endDate = document.getElementById('modal_end_time').value;
      const wordCount = document.getElementById('modal_word_count').value;
      const rating = document.getElementById('modal_rating').value;
      const comment = document.getElementById('modal_comment').value;

      if (!bookName || !author || !status) {
        showErrorMessage('书名、作者和状态为必填项。');
        return;
      }

      const fields = {
        'book_name': bookName,
        'author': author,
        'type': type,
        'status': status,
        'channel': channel,
        'number_font': wordCount ? parseFloat(wordCount) : null,
        'rating': rating,
        'comment': comment
      };

      // 处理日期字段
      if (startDate) {
        fields['start_time'] = Math.floor(new Date(startDate).getTime() / 1000);
      }
      if (endDate) {
        fields['end_time'] = Math.floor(new Date(endDate).getTime() / 1000);
      }

      const { token, datasheetId } = API_CONFIG;
      let url = `https://api.vika.cn/fusion/v1/datasheets/${datasheetId}/records`;
      let method = 'POST';
      let body = { records: [{ fields }] };

      if (bookId) {
        url = `${url}/${bookId}`;
        method = 'PUT';
        body = { fields };
      }

      try {
        showLoadingOverlay(bookId ? '正在更新书籍...' : '正在添加书籍...');
        const response = await fetch(url, {
          method: method,
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(body)
        });

        if (response.ok) {
          showSuccessMessage(`书籍${bookId ? '更新' : '添加'}成功！`);
          hideAddBookModal();
          await loadBooks();
        } else {
          const errorData = await response.json();
          throw new Error(`API错误: ${errorData.message || response.statusText}`);
        }
      } catch (error) {
        console.error('保存书籍失败:', error);
        showErrorMessage(`保存书籍失败: ${error.message}`);
      } finally {
        hideLoadingOverlay();
      }
    }

    // 自动补全功能
    function setupAutocompleteInput(inputId, suggestionsId, dataKey) {
      const input = document.getElementById(inputId);
      const suggestions = document.getElementById(suggestionsId);

      input.addEventListener('input', () => {
        const value = input.value.toLowerCase();
        if (!value) {
          suggestions.innerHTML = '';
          suggestions.classList.add('hidden');
          return;
        }

        const filtered = autocompleteData[dataKey].filter(item =>
          item.toLowerCase().includes(value)
        );

        if (filtered.length === 0) {
          suggestions.innerHTML = `<div class="py-2 px-4 text-gray-500">未找到匹配项</div>`;
          suggestions.classList.remove('hidden');
          return;
        }

        suggestions.innerHTML = filtered.map(item =>
          `<div class="py-2 px-4 hover:bg-gray-100 cursor-pointer" onclick="selectSuggestion(this, '${inputId}', '${suggestionsId}')">${item}</div>`
        ).join('');
        suggestions.classList.remove('hidden');
      });

      input.addEventListener('blur', () => {
        setTimeout(() => {
          suggestions.classList.add('hidden');
        }, 200);
      });
    }

    function selectSuggestion(element, inputId, suggestionsId) {
      document.getElementById(inputId).value = element.textContent;
      document.getElementById(suggestionsId).classList.add('hidden');
    }

    function initModalAutocomplete() {
      setupAutocompleteInput('modal_book_name', 'modal_book_name_suggestions', 'book_names');
      setupAutocompleteInput('modal_author', 'modal_author_suggestions', 'authors');
      setupAutocompleteInput('modal_type', 'modal_type_suggestions', 'types');
    }

    // 版本信息功能
    function showVersionModal() {
      const versionInfoModal = document.getElementById('versionInfoModal');
      const versionContent = document.getElementById('versionContent');
      if (versionInfoModal && versionContent) {
        versionContent.innerHTML = `
          <h3 class="text-lg font-bold mb-2">v0.0.6 20250715 版本更新</h3>
          <ul class="list-disc list-inside text-gray-700">
            <li><strong>修改</strong>：弃了按钮备注逻辑优化，保留原有备注并追加新的信息。</li>
            <li><strong>优化</strong>：页面顶部"阅读"标题现已作为强制刷新按钮，点击可刷新缓存。</li>
            <li><strong>新增</strong>：版本信息按钮及模态框，用于展示版本更新记录。</li>
          </ul>
        `;
        versionInfoModal.classList.remove('hidden');
      }
    }

    function hideVersionModal() {
      const versionInfoModal = document.getElementById('versionInfoModal');
      if (versionInfoModal) {
        versionInfoModal.classList.add('hidden');
      }
    }

    // 事件监听器设置
    function setupEventListeners() {
      // 搜索功能
      searchInput.addEventListener('input', debounce(filterBooks, 300));
      statusFilter.addEventListener('change', filterBooks);
      channelFilter.addEventListener('change', filterBooks);
      clearFiltersBtn.addEventListener('click', clearFilters);

      // 模态框控制
      document.getElementById('add-book-btn').addEventListener('click', showAddBookModal);
      document.getElementById('close-add-modal').addEventListener('click', hideAddBookModal);
      document.getElementById('cancel-add-book').addEventListener('click', hideAddBookModal);
      document.getElementById('add-book-form').addEventListener('submit', handleAddBookSubmit);

      // 确认模态框
      document.getElementById('confirm-cancel').addEventListener('click', hideConfirmModal);
      document.getElementById('confirm-ok').addEventListener('click', executeConfirmedAction);

      // 模态窗口外部点击关闭
      document.getElementById('add-book-modal').addEventListener('click', function(e) {
        if (e.target === this) {
          hideAddBookModal();
        }
      });

      // 版本信息
      document.getElementById('versionInfoBtn').addEventListener('click', showVersionModal);
      document.getElementById('closeVersionInfoModal').addEventListener('click', hideVersionModal);
      document.getElementById('closeVersionInfoModalBottom').addEventListener('click', hideVersionModal);

      // 版本信息模态框外部点击关闭
      document.getElementById('versionInfoModal').addEventListener('click', function(e) {
        if (e.target === this) {
          hideVersionModal();
        }
      });

      // 强制刷新按钮
      document.getElementById('refreshButton').addEventListener('click', () => {
        refreshCache(true);
        showToast('缓存已强制刷新！', 'success');
      });
    }

    // 初始化函数
    async function init() {
      // 初始化DOM元素
      loadingState = document.getElementById('loading-state');
      emptyState = document.getElementById('empty-state');
      booksContainer = document.getElementById('books-container');
      searchInput = document.getElementById('search-input');
      statusFilter = document.getElementById('status-filter');
      channelFilter = document.getElementById('channel-filter');
      clearFiltersBtn = document.getElementById('clear-filters');
      pagination = document.getElementById('pagination');
      confirmModal = document.getElementById('confirm-modal');
      loadingOverlay = document.getElementById('loading-overlay');
      addBookModal = document.getElementById('add-book-modal');
      addBookForm = document.getElementById('add-book-form');

      // 设置事件监听器
      setupEventListeners();

      // 加载书籍数据
      await loadBooks();

      // 显示缓存状态
      showCacheStatus();
    }

    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', init);
  </script>
</body>
</html>