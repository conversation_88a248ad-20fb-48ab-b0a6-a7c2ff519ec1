const fs = require('fs');

// 读取JavaScript文件
const jsContent = fs.readFileSync('script-5.js', 'utf8');
const lines = jsContent.split('\n');

console.log('Checking brace matching...');

let openBraces = 0;
let openParens = 0;
let openBrackets = 0;

for (let i = 0; i < lines.length; i++) {
  const line = lines[i];
  
  for (let j = 0; j < line.length; j++) {
    const char = line[j];
    
    switch (char) {
      case '{':
        openBraces++;
        break;
      case '}':
        openBraces--;
        if (openBraces < 0) {
          console.log(`❌ Extra closing brace at line ${i + 1}, column ${j + 1}`);
          console.log(`Line: ${line}`);
          return;
        }
        break;
      case '(':
        openParens++;
        break;
      case ')':
        openParens--;
        if (openParens < 0) {
          console.log(`❌ Extra closing parenthesis at line ${i + 1}, column ${j + 1}`);
          console.log(`Line: ${line}`);
          return;
        }
        break;
      case '[':
        openBrackets++;
        break;
      case ']':
        openBrackets--;
        if (openBrackets < 0) {
          console.log(`❌ Extra closing bracket at line ${i + 1}, column ${j + 1}`);
          console.log(`Line: ${line}`);
          return;
        }
        break;
    }
  }
}

console.log(`Final counts:`);
console.log(`Open braces: ${openBraces}`);
console.log(`Open parentheses: ${openParens}`);
console.log(`Open brackets: ${openBrackets}`);

if (openBraces > 0) {
  console.log(`❌ Missing ${openBraces} closing brace(s)`);
} else if (openBraces < 0) {
  console.log(`❌ Extra ${-openBraces} closing brace(s)`);
}

if (openParens > 0) {
  console.log(`❌ Missing ${openParens} closing parenthesis(es)`);
} else if (openParens < 0) {
  console.log(`❌ Extra ${-openParens} closing parenthesis(es)`);
}

if (openBrackets > 0) {
  console.log(`❌ Missing ${openBrackets} closing bracket(s)`);
} else if (openBrackets < 0) {
  console.log(`❌ Extra ${-openBrackets} closing bracket(s)`);
}

if (openBraces === 0 && openParens === 0 && openBrackets === 0) {
  console.log('✅ All braces, parentheses, and brackets are balanced');
}
