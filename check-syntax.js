const fs = require('fs');

// 读取HTML文件
const htmlContent = fs.readFileSync('book.html', 'utf8');

// 提取script标签中的JavaScript代码
const scriptMatches = htmlContent.match(/<script[^>]*>([\s\S]*?)<\/script>/g);

if (scriptMatches) {
  scriptMatches.forEach((script, index) => {
    // 提取script标签内的内容
    const jsContent = script.replace(/<script[^>]*>/, '').replace(/<\/script>/, '');

    // 跳过外部脚本和简单的初始化脚本
    if (jsContent.trim().length < 1000) {
      return;
    }

    console.log(`\n=== Script ${index + 1} ===`);
    console.log(`Length: ${jsContent.length} characters`);

    try {
      // 尝试解析JavaScript代码
      new Function(jsContent);
      console.log('✅ Syntax is valid');
    } catch (error) {
      console.log('❌ Syntax error:', error.message);

      // 保存JavaScript代码到文件以便调试
      fs.writeFileSync(`script-${index + 1}.js`, jsContent);
      console.log(`JavaScript code saved to script-${index + 1}.js`);

      // 尝试找到错误位置
      const lines = jsContent.split('\n');
      console.log(`Total lines: ${lines.length}`);

      // 显示最后几行代码
      console.log('\nLast 10 lines:');
      for (let i = Math.max(0, lines.length - 10); i < lines.length; i++) {
        console.log(`${i + 1}: ${lines[i]}`);
      }
    }
  });
} else {
  console.log('No script tags found');
}
