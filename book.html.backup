<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>书籍列表 - 阅读记录</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link href="https://cdn.bootcdn.net/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css" rel="stylesheet">
  <script charset="UTF-8" id="LA_COLLECT" src="//sdk.51.la/js-sdk-pro.min.js"></script>
  <script>LA.init({id:"Jqud4Iv4jX9nBtrP",ck:"Jqud4Iv4jX9nBtrP"})</script>
  
  <!-- Tailwind 配置 -->
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: '#3B82F6',
            secondary: '#6366F1',
            success: '#10B981',
            danger: '#EF4444',
            warning: '#F59E0B',
            info: '#06B6D4',
            light: '#F3F4F6',
            dark: '#1F2937'
          },
          fontFamily: {
            inter: ['Inter', 'system-ui', 'sans-serif']
          }
        }
      }
    }
  </script>
  
  <style type="text/tailwindcss">
    @layer utilities {
      .content-auto {
        content-visibility: auto;
      }
      .card-shadow {
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
      }
      .card-hover {
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        transform: translateY(-1px);
        transition: all 300ms ease;
      }
      .btn-hover {
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        transform: translateY(-0.5px);
        transition: all 300ms ease;
      }
      .status-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.625rem 0.25rem;
        border-radius: 9999px;
        font-size: 0.75rem;
        line-height: 1rem;
        font-weight: 500;
      }
      .channel-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        font-size: 0.75rem;
        line-height: 1rem;
        font-weight: 500;
      }
  </style>
</head>
<body class="bg-gradient-to-br from-blue-50 to-indigo-50 min-h-screen font-inter text-dark">
  <div class="container mx-auto px-4 py-8 max-w-7xl">
    <!-- 头部区域 -->
    <header class="mb-8">
      <div class="flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
          <h1 id="refreshButton" class="text-[clamp(1.8rem,4vw,2.8rem)] font-bold text-transparent bg-clip-text bg-gradient-to-r from-primary to-secondary mb-2 cursor-pointer">
            阅读
          </h1>
          <p class="text-gray-600 text-lg">
            小王这辈子能看多少书啊！
          </p>
        </div>
        <div class="mt-4 md:mt-0">
          <button id="add-book-btn" class="inline-flex items-center px-6 py-3 bg-primary text-white rounded-lg btn-hover">
            <i class="fa fa-plus mr-2"></i>添加新书
          </button>
        </div>
      </div>
    </header>

    <!-- 搜索和筛选区域 -->
    <div class="bg-white rounded-xl shadow-lg p-6 mb-8 card-shadow">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">搜索书籍</label>
          <input type="text" id="search-input" placeholder="书名或作者..." 
                 class="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-primary/50 focus:border-primary focus:outline-none transition-all">
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">阅读状态</label>
          <select id="status-filter" class="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-primary/50 focus:border-primary focus:outline-none transition-all">
            <option value="">全部状态</option>
            <option value="1">看完</option>
            <option value="0">在看</option>
            <option value="2">弃了</option>
          </select>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">阅读渠道</label>
          <select id="channel-filter" class="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-primary/50 focus:border-primary focus:outline-none transition-all">
            <option value="">全部渠道</option>
            <option value="1">微信读书</option>
            <option value="2">Readest</option>
            <option value="3">Kindle</option>
            <option value="4">实体书</option>
            <option value="5">图书馆</option>
            <option value="6">iPad</option>
            <option value="7">番茄</option>
          </select>
        </div>
        <div class="flex items-end">
          <button id="clear-filters" class="w-full px-4 py-2 bg-gray-200 text-gray-700 rounded-lg btn-hover">
            <i class="fa fa-refresh mr-2"></i>重置筛选
          </button>
        </div>
      </div>
    </div>

    <!-- 统计信息 -->
    <div class="grid grid-cols-1 md:grid-cols-6 gap-6 mb-8">
      <!-- 总书籍数 -->
      <div class="bg-white rounded-xl p-6 card-shadow">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-primary/10">
            <i class="fa fa-book text-primary text-xl"></i>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">总书籍数</p>
            <p class="text-2xl font-bold text-gray-900" id="total-books">0</p>
          </div>
        </div>
      </div>
      <!-- 新增：总字数卡片 -->
      <div class="bg-white rounded-xl p-6 card-shadow">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-info/10">
            <i class="fa fa-file-text-o text-info text-xl"></i>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">总字数</p>
            <p class="text-2xl font-bold text-gray-900" id="total-word-count">0</p>
          </div>
        </div>
      </div>
      <!-- 已读完 -->
      <div class="bg-white rounded-xl p-6 card-shadow">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-success/10">
            <i class="fa fa-check-circle text-success text-xl"></i>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">已读完</p>
            <p class="text-2xl font-bold text-gray-900" id="completed-books">0</p>
          </div>
        </div>
      </div>
      <div class="bg-white rounded-xl p-6 card-shadow">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-warning/10">
            <i class="fa fa-clock-o text-warning text-xl"></i>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">阅读中</p>
            <p class="text-2xl font-bold text-gray-900" id="reading-books">0</p>
          </div>
        </div>
      </div>
      <div class="bg-white rounded-xl p-6 card-shadow">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-danger/10">
            <i class="fa fa-times-circle text-danger text-xl"></i>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">弃了</p>
            <p class="text-2xl font-bold text-gray-900" id="abandoned-books">0</p>
          </div>
        </div>
      </div>
      <div class="bg-white rounded-xl p-6 card-shadow">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-info/10">
            <i class="fa fa-calendar text-info text-xl"></i>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">本月读完</p>
            <p class="text-2xl font-bold text-gray-900" id="monthly-books">0</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 书籍列表 -->
    <div class="bg-white rounded-xl shadow-lg overflow-hidden card-shadow">
      <div class="px-6 py-4 border-b border-gray-200">
        <h2 class="text-xl font-semibold text-gray-900 flex items-center">
          <i class="fa fa-list mr-2 text-primary"></i>书籍列表
          <span class="ml-2 text-sm font-normal text-gray-500" id="books-count">(0 本书)</span>
          <span id="cache-status" class="ml-2 text-sm font-normal text-gray-500"></span>
        </h2>
      </div>
      
      <!-- 加载状态 -->
      <div id="loading-state" class="p-8 text-center">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
        <p class="text-gray-600">正在加载书籍列表...</p>
      </div>
      
      <!-- 空状态 -->
      <div id="empty-state" class="p-8 text-center hidden">
        <div class="text-gray-400 mb-4">
          <i class="fa fa-book text-6xl"></i>
        </div>
        <h3 class="text-lg font-medium text-gray-900 mb-2">暂无书籍记录</h3>
        <p class="text-gray-600 mb-4">开始添加您的第一本书吧！</p>
        <button onclick="showAddBookModal()" class="inline-flex items-center px-4 py-2 bg-primary text-white rounded-lg btn-hover">
          <i class="fa fa-plus mr-2"></i>添加书籍
        </button>
      </div>
      
      <!-- 表格头部 -->
      <div id="table-header" class="px-6 py-3 bg-gray-50 border-b border-gray-200 hidden">
        <div class="grid grid-cols-1 lg:grid-cols-12 gap-4 items-center text-sm font-medium text-gray-700">
          <div class="lg:col-span-1">ID</div>
          <div class="lg:col-span-2">书籍信息</div>
          <div class="lg:col-span-1">字数</div>
          <div class="lg:col-span-1">状态</div>
          <div class="lg:col-span-1">渠道</div>
          <div class="lg:col-span-1">开始时间</div>
          <div class="lg:col-span-1">结束时间</div>
          <div class="lg:col-span-1">用时</div>
          <div class="lg:col-span-1">备注</div>
          <div class="lg:col-span-2">操作</div>
        </div>
      </div>

      <!-- 书籍卡片容器 -->
      <div id="books-container" class="p-6 space-y-4 hidden">
        <!-- 书籍卡片将通过JavaScript动态生成 -->
      </div>
    </div>

    <!-- 分页 -->
    <div id="pagination" class="mt-8 flex justify-center hidden">
      <nav class="flex items-center space-x-2">
        <button id="prev-page" class="px-3 py-2 text-gray-500 bg-white rounded-lg border border-gray-300 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
          <i class="fa fa-chevron-left"></i>
        </button>
        <div id="page-numbers" class="flex space-x-1">
          <!-- 页码按钮将通过JavaScript动态生成 -->
        </div>
        <button id="next-page" class="px-3 py-2 text-gray-500 bg-white rounded-lg border border-gray-300 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
          <i class="fa fa-chevron-right"></i>
        </button>
      </nav>
    </div>
  </div>

  <!-- 确认对话框 -->
  <div id="confirm-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-lg p-6 shadow-xl max-w-md w-full mx-4">
      <div class="flex items-center mb-4">
        <div class="p-3 rounded-full bg-warning/10 mr-4">
          <i class="fa fa-exclamation-triangle text-warning text-xl"></i>
        </div>
        <div>
          <h3 class="text-lg font-medium text-gray-900">确认操作</h3>
          <p class="text-gray-600 mt-1" id="confirm-message">确定要执行此操作吗？</p>
        </div>
      </div>
      <div class="flex justify-end space-x-3">
        <button id="confirm-cancel" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg btn-hover">
          取消
        </button>
        <button id="confirm-ok" class="px-4 py-2 bg-primary text-white rounded-lg btn-hover">
          确定
        </button>
      </div>
    </div>
  </div>

  <!-- 添加书籍模态窗口 -->
  <div id="add-book-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-xl shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
      <div class="p-6">
        <div class="flex items-center justify-between mb-6">
          <h2 class="text-2xl font-bold text-gray-900">添加新书</h2>
          <button id="close-add-modal" class="text-gray-400 hover:text-gray-600">
            <i class="fa fa-times text-xl"></i>
          </button>
        </div>
        
        <form id="add-book-form" class="space-y-6">
          <input type="hidden" id="edit-book-id" name="id">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="relative">
              <label for="modal_book_name" class="block text-sm font-medium text-gray-700 mb-1">书籍名称 *</label>
              <input type="text" id="modal_book_name" name="book_name" placeholder="请输入书籍名称"
                     class="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-primary/50 focus:border-primary focus:outline-none transition-all" required autocomplete="off">
              <div id="modal_book_name_suggestions" class="absolute z-10 w-full bg-white border border-gray-300 rounded-lg shadow-lg max-h-48 overflow-y-auto hidden"></div>
            </div>

            <div class="relative">
              <label for="modal_author" class="block text-sm font-medium text-gray-700 mb-1">作者 *</label>
              <input type="text" id="modal_author" name="author" placeholder="请输入作者姓名"
                     class="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-primary/50 focus:border-primary focus:outline-none transition-all" required autocomplete="off">
              <div id="modal_author_suggestions" class="absolute z-10 w-full bg-white border border-gray-300 rounded-lg shadow-lg max-h-48 overflow-y-auto hidden"></div>
            </div>
            
            <div class="relative">
              <label for="modal_type" class="block text-sm font-medium text-gray-700 mb-1">书籍类型 *</label>
              <input type="text" id="modal_type" name="type" placeholder="例如: 商业、历史"
                     class="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-primary/50 focus:border-primary focus:outline-none transition-all" required autocomplete="off">
              <div id="modal_type_suggestions" class="absolute z-10 w-full bg-white border border-gray-300 rounded-lg shadow-lg max-h-48 overflow-y-auto hidden"></div>
            </div>
            
            <div>
              <label for="modal_channel" class="block text-sm font-medium text-gray-700 mb-1">阅读渠道 *</label>
              <select id="modal_channel" name="channel" 
                      class="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-primary/50 focus:border-primary focus:outline-none transition-all" required>
                <option value="1">微信读书</option>
                <option value="2">Readest</option>
                <option value="3">Kindle</option>
                <option value="4">实体书</option>
                <option value="5">图书馆</option>
                <option value="6">iPad</option>
                <option value="7">番茄小说</option>
              </select>
            </div>
            
            <div>
              <label for="modal_number_font" class="block text-sm font-medium text-gray-700 mb-1">字数</label>
              <input type="number" id="modal_number_font" name="number_font" placeholder="请输入字数"
                     class="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-primary/50 focus:border-primary focus:outline-none transition-all" min="0" step="any">
            </div>
          </div>

          <div>
            <label for="modal_remark" class="block text-sm font-medium text-gray-700 mb-1">备注</label>
            <textarea id="modal_remark" name="remark" rows="3" placeholder="请输入备注信息（可选）"
                      class="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-primary/50 focus:border-primary focus:outline-none transition-all resize-none"></textarea>
          </div>
          
          <div class="flex justify-end space-x-4 pt-4 border-t border-gray-100">
            <button type="button" id="cancel-add-book" class="px-6 py-2.5 bg-gray-200 text-gray-700 rounded-lg btn-hover">
              <i class="fa fa-times mr-2"></i>取消
            </button>
            <button type="submit" class="px-6 py-2.5 bg-primary text-white rounded-lg btn-hover">
              <i class="fa fa-save mr-2"></i>保存记录
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <!-- 加载遮罩 -->
  <div id="loading-overlay" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-lg p-6 shadow-xl max-w-md w-full">
      <div class="flex items-center">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mr-4"></div>
        <div>
          <h3 class="text-lg font-medium text-gray-900">处理中...</h3>
          <p class="text-gray-500 mt-1" id="loading-message">正在更新数据</p>
        </div>
      </div>
    </div>
  </div>

  <footer class="mt-12 text-center text-gray-500 text-sm pb-6">
    <p>© 2025 阅读记录 | 记录每一本好书的阅读历程</p>
    <button id="versionInfoBtn" class="text-sm text-gray-500 hover:text-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">v0.0.6 20250715</button>
  </footer>

  <script>
    /**
     * @constant {object} API_CONFIG - Vika API 配置。
     * @property {string} token - Vika API 的认证令牌。
     * @property {string} datasheetId - 数据表 ID。
     * @property {string} viewId - 视图 ID。
     */
    const API_CONFIG = {
      token: 'uskcZUvxWXvLIPXN0hUC6DK',
      datasheetId: 'dsthg9PyzRB9ABCVbN',
      viewId: 'viwyftA9tfZLb'
    };

    /**
     * @global
     * @type {Array<Object>} allBooks - 存储所有书籍数据的数组。
     */
    let allBooks = [];
    /**
     * @global
     * @type {Array<Object>} filteredBooks - 存储经过筛选后的书籍数据的数组。
     */
    let filteredBooks = [];
    /**
     * @global
     * @type {number} currentPage - 当前页码。
     */
    let currentPage = 1;
    /**
     * @constant {number} booksPerPage - 每页显示的书籍数量。
     */
    const booksPerPage = 10;

    /**
     * @constant {string} CACHE_KEY - 缓存书籍数据的键名。
     */
    const CACHE_KEY = 'booksCache';
    /**
     * @constant {number} CACHE_TTL - 数据缓存的有效期，单位为毫秒（24小时）。
     */
    const CACHE_TTL = 24 * 60 * 60 * 1000; // 数据缓存有效期为 24 小时

    /**
     * @function isCacheValid
     * @description 检查本地缓存是否有效。
     * 缓存的有效性基于时间戳和预设的有效期 `CACHE_TTL`。
     * @returns {boolean} 如果缓存有效则返回 `true`，否则返回 `false`。
     */
    function isCacheValid() {
      const cacheTimestamp = localStorage.getItem(`${CACHE_KEY}_timestamp`);
      if (!cacheTimestamp) return false;
      return Date.now() - parseInt(cacheTimestamp) < CACHE_TTL;
    }

    /**
     * @function loadBooksFromCache
     * @description 尝试从本地缓存加载书籍数据。
     * 如果缓存存在且有效，则解析缓存数据并更新全局书籍列表、统计信息和UI。
     * @returns {boolean} 如果成功从缓存加载数据则返回 `true`，否则返回 `false`。
     */
    function loadBooksFromCache() {
      try {
        const cachedData = localStorage.getItem(CACHE_KEY);
        if (!cachedData || !isCacheValid()) {
          hideLoadingOverlay();
          showEmptyState();
          return false;
        }
        
        const books = JSON.parse(cachedData);
        allBooks = books; // 确保 allBooks 被更新
        filteredBooks = [...allBooks]; // 确保 filteredBooks 被更新
        updateStatistics(); // 更新统计信息
        renderBooks(); // 使用 renderBooks 替代 displayBooks
        hideLoadingOverlay();
        return true;
      } catch (error) {
        console.error('加载书籍失败:', error);
        showToast('加载书籍失败，请检查网络或API配置！', 'error');
      } finally {
        hideLoadingOverlay(); // 确保在加载完成后隐藏加载提示
      }
      return false; // 如果缓存无效或加载失败，返回 false
    }

    /**
     * @function saveBooksToCache
     * @description 将书籍数据保存到本地缓存。
     * 同时更新缓存的时间戳。
     * @param {Array<Object>} books - 要保存的书籍数据数组。
     */
    function saveBooksToCache(books) {
      localStorage.setItem(CACHE_KEY, JSON.stringify(books));
      localStorage.setItem(`${CACHE_KEY}_timestamp`, Date.now().toString());
    }

    /**
     * @function clearExpiredCache
     * @description 清除本地存储中过期的书籍缓存。
     * 如果缓存时间戳存在但已过期，则移除缓存数据和时间戳。
     */
    function clearExpiredCache() {
      const cacheTimestamp = localStorage.getItem(`${CACHE_KEY}_timestamp`);
      if (cacheTimestamp && !isCacheValid()) {
        localStorage.removeItem(CACHE_KEY);
        localStorage.removeItem(`${CACHE_KEY}_timestamp`);
      }

    /**
     * @function refreshCache
     * @description 手动刷新书籍数据缓存。
     * 清除现有缓存，然后重新加载书籍数据并显示加载状态。
     */
    /**
     * @function refreshCache
     * @description 手动刷新书籍数据缓存。
     * @param {boolean} [force=false] - 是否强制刷新，忽略缓存有效性检查。
     * @returns {void}
     */
    function refreshCache(force = false) {
      if (force || !isCacheValid()) {
        localStorage.removeItem(CACHE_KEY);
        localStorage.removeItem(`${CACHE_KEY}_timestamp`);
        loadBooks();
        showLoadingOverlay('正在从云端加载最新数据...');
        document.getElementById('cache-status').textContent = '（正在从云端加载最新数据）';
      } else {
        showToast('缓存数据仍然有效，无需刷新', 'info');
      }

    /**
     * @function showCacheStatus
     * @description 显示当前缓存状态到页面上。
     * 根据缓存的有效性显示缓存更新时间或提示当前使用的是云端数据。
     */
    function showCacheStatus() {
      const cacheTimestamp = localStorage.getItem(`${CACHE_KEY}_timestamp`);
      if (cacheTimestamp && isCacheValid()) {
        const lastUpdated = new Date(parseInt(cacheTimestamp)).toLocaleString();
        document.getElementById('cache-status').textContent = `（缓存更新时间：${lastUpdated}）`;
      } else {
        document.getElementById('cache-status').textContent = '（当前使用的是云端数据）';
      }
    }

    // 加载书籍数据
    /**
     * @function loadBooks
     * @description 异步加载书籍数据，优先从缓存加载，否则从 Vika API 获取。
     * 处理分页、数据更新、缓存管理、自动完成数据加载和UI渲染。
     * @async
     * @returns {Promise<void>} 无返回值。
     */
    async function loadBooks() {
      try {
        showLoadingOverlay('正在加载书籍...');
        clearExpiredCache(); // 清理过期缓存

        // 尝试从缓存加载数据
        if (loadBooksFromCache()) {
          console.log('使用缓存数据');
          return;
        }

        allBooks = [];

        const { token, datasheetId, viewId } = API_CONFIG;
        let pageToken = null;
        let hasMore = true;

        // 循环获取所有数据
        while (hasMore) {
          let url = `https://api.vika.cn/fusion/v1/datasheets/${datasheetId}/records?pageSize=1000`;
          if (viewId) url += `&viewId=${viewId}`;
          if (pageToken) url += `&pageToken=${pageToken}`;

          const response = await fetch(url, {
            method: 'GET',
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            }
          });

          if (response.ok) {
            const result = await response.json();
            const records = result.data?.records || [];
            allBooks = allBooks.concat(records);
            console.log('API 请求成功，获取到记录数:', records.length);

            // 检查是否还有更多数据
            pageToken = result.data?.pageToken;
            hasMore = !!pageToken;
            console.log('pageToken:', pageToken, 'hasMore:', hasMore);
          } else {
            console.error('API 请求失败，状态码:', response.status);
            throw new Error(`加载数据失败，状态码: ${response.status}`);
          }
        }

        // 更新缓存
        saveBooksToCache(allBooks);

        // 加载自动完成数据并初始化模态窗口的自动完成功能
        loadAutocompleteData();
        initModalAutocomplete();

        filteredBooks = [...allBooks];
        updateStatistics();
        renderBooks();
      } catch (error) {
        console.error('加载书籍失败:', error);
        showToast('加载书籍失败，请检查网络或API配置！', 'error');
      } finally {
        hideLoadingOverlay(); // 确保在加载完成后隐藏加载提示
      }
      showEmptyState();

    // 自动完成数据缓存
    let autocompleteData = {
      book_names: [],
      authors: [],
      types: []
    };

    /**
     * @function loadAutocompleteData
     * @description 从 `allBooks` 中提取书籍名称、作者和类型，并填充到 `autocompleteData` 对象中。
     *              用于自动补全功能的数据源。
     * @returns {void}
     */
    function loadAutocompleteData() {
      const bookNames = new Set();
      const authors = new Set();
      const types = new Set();

      allBooks.forEach(book => {
        if (book.fields.book_name) bookNames.add(book.fields.book_name);
        if (book.fields.author) authors.add(book.fields.author);
        if (book.fields.type) types.add(book.fields.type);
      });

      autocompleteData.book_names = Array.from(bookNames);
      autocompleteData.authors = Array.from(authors);
      autocompleteData.types = Array.from(types);
    }

    /**
     * @function initModalAutocomplete
     * @description 初始化模态框中的自动补全输入框。
     *              在书籍添加/编辑模态框显示时调用，确保自动补全功能可用。
     * @returns {void}
     */
    function initModalAutocomplete() {
      setupAutocompleteInput('modal_book_name', 'modal_book_name_suggestions', 'book_names');
      setupAutocompleteInput('modal_author', 'modal_author_suggestions', 'authors');
      setupAutocompleteInput('modal_type', 'modal_type_suggestions', 'types');
    }

    // 渠道映射
    const channelMap = {
      '1': { name: '微信读书', color: 'bg-green-100 text-green-800' },
      '2': { name: 'Readest', color: 'bg-blue-100 text-blue-800' },
      '3': { name: 'Kindle', color: 'bg-orange-100 text-orange-800' },
      '4': { name: '实体书', color: 'bg-purple-100 text-purple-800' },
      '5': { name: '图书馆', color: 'bg-indigo-100 text-indigo-800' },
      '6': { name: 'iPad', color: 'bg-gray-100 text-gray-800' },
      '7': { name: '番茄', color: 'bg-gray-100 text-gray-800' }
    };

    // DOM元素
    const loadingState = document.getElementById('loading-state');
    const emptyState = document.getElementById('empty-state');
    const booksContainer = document.getElementById('books-container');
    const tableHeader = document.getElementById('table-header');
    const searchInput = document.getElementById('search-input');
    const statusFilter = document.getElementById('status-filter');
    const channelFilter = document.getElementById('channel-filter');
    const clearFiltersBtn = document.getElementById('clear-filters');
    const pagination = document.getElementById('pagination');
    const confirmModal = document.getElementById('confirm-modal');
    const loadingOverlay = document.getElementById('loading-overlay');

    /**
     * @function showLoadingOverlay
     * @description 显示加载覆盖层，并在其中显示指定消息。
     * @param {string} message - 加载时显示的消息。
     */
    function showLoadingOverlay(message) {
      document.getElementById('loading-message').textContent = message;
      loadingOverlay.classList.remove('hidden');
    }

    /**
     * @function hideLoadingOverlay
     * @description 隐藏加载覆盖层。
     */
    function hideLoadingOverlay() {
        loadingOverlay.classList.add('hidden');
     }

    /**
     * @function showEmptyState
     * @description 显示空状态提示。
     * @returns {void}
     */
    function showEmptyState() {
      emptyState.classList.remove('hidden');
      booksContainer.innerHTML = ''; // 清空书籍列表
      pagination.innerHTML = ''; // 清空分页
    }

    /**
     * @function hideEmptyState
     * @description 隐藏空状态提示。
     * @returns {void}
     */
    function hideEmptyState() {
      emptyState.classList.add('hidden');
    }

     /**
      * @function showSuccessMessage
      * @description 显示一个成功的提示消息。
      * @param {string} message - 要显示的成功消息。
      */
     function showSuccessMessage(message) {
       alert('✅ ' + message);
     }

     /**
      * @function showErrorMessage
      * @description 显示一个错误的提示消息。
      * @param {string} message - 要显示的错误消息。
      */
     function showErrorMessage(message) {
       alert('❌ ' + message);
     }

    /**
     * @function showToast
     * @description 显示一个短暂的提示消息（Toast）。
     * @param {string} message - 要显示的消息。
     * @param {string} type - 消息类型，可以是 'success', 'error', 'info', 'warning'。
     */
    function showToast(message, type = 'info') {
      const toastContainer = document.getElementById('toast-container');
      if (!toastContainer) {
        console.warn('Toast container not found.');
        return;
      }

      const toast = document.createElement('div');
      toast.className = `fixed bottom-4 right-4 p-4 rounded-lg shadow-lg text-white z-50`;

      switch (type) {
        case 'success':
          toast.classList.add('bg-green-500');
          break;
        case 'error':
          toast.classList.add('bg-red-500');
          break;
        case 'warning':
          toast.classList.add('bg-yellow-500');
          break;
        case 'info':
        default:
          toast.classList.add('bg-blue-500');
          break;
      }

      toast.textContent = message;
      toastContainer.appendChild(toast);

      setTimeout(() => {
        toast.remove();
      }, 3000); // 3秒后自动消失
    }

    /**
     * 设置所有事件监听器。
     * 包括搜索、筛选、清除筛选、确认对话框、添加书籍模态窗口等。
     */
    /**
     * @function showAddBookModal
     * @description 显示添加/编辑书籍模态框。
     */
    function showAddBookModal() {
      addBookModal.classList.remove('hidden');
      addBookForm.reset(); // 清空表单
      document.getElementById('edit-book-id').value = ''; // 清空编辑ID
      initModalAutocomplete(); // 初始化模态框中的自动补全
    }

    /**
     * @function hideAddBookModal
     * @description 隐藏添加/编辑书籍模态框，并重置表单。
     */
    function hideAddBookModal() {
      addBookModal.classList.add('hidden');
      addBookForm.reset(); // 重置表单
      document.getElementById('edit-book-id').value = ''; // 清空编辑ID
    }

    /**
     * @function setupEventListeners
     * @description 设置页面上所有主要的事件监听器，包括搜索、筛选、模态框的显示/隐藏、表单提交等。
     * @returns {void}
     */
    function setupEventListeners() {

      // 搜索功能
      searchInput.addEventListener('input', debounce(filterBooks, 300));

      // 筛选功能
      statusFilter.addEventListener('change', filterBooks);
      channelFilter.addEventListener('change', filterBooks);

      // 清除筛选
      clearFiltersBtn.addEventListener('click', clearFilters);

      // 确认对话框
      document.getElementById('confirm-cancel').addEventListener('click', hideConfirmModal);
      document.getElementById('confirm-ok').addEventListener('click', executeConfirmedAction);

      // 添加书籍模态窗口
      document.getElementById('add-book-btn').addEventListener('click', showAddBookModal);
      document.getElementById('close-add-modal').addEventListener('click', hideAddBookModal);
      document.getElementById('cancel-add-book').addEventListener('click', hideAddBookModal);
      document.getElementById('add-book-form').addEventListener('submit', handleAddBookSubmit);

      // 模态窗口外部点击关闭
      document.getElementById('add-book-modal').addEventListener('click', function(e) {
        if (e.target === this) {
          hideAddBookModal();
        }
      });
    }

    /**
     * @function debounce
     * @description 防抖函数，用于限制函数调用的频率，在一定时间内只执行最后一次调用。
     * @param {Function} func - 要防抖的函数。
     * @param {number} wait - 延迟时间（毫秒）。
     * @returns {Function} 防抖后的函数。
     */
    function debounce(func, wait) {
      let timeout;
      return function executedFunction(...args) {
        const later = () => {
          clearTimeout(timeout);
          func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
      };
    }

    /**
     * @function showConfirmModal
     * @description 显示确认模态框，并设置确认回调函数。
     * @param {string} message - 模态框中显示的消息。
     * @param {Function} onConfirm - 用户点击确认按钮时执行的回调函数。
     * @returns {void}
     */
    function showConfirmModal(message, onConfirm) {
      document.getElementById('confirm-message').textContent = message;
      confirmModal.classList.remove('hidden');

      // 存储确认回调
      window.pendingConfirmAction = onConfirm;
    }

    /**
     * @function hideConfirmModal
     * @description 隐藏确认模态框。
     * @returns {void}
     */
    function hideConfirmModal() {
      confirmModal.classList.add('hidden');
      window.pendingConfirmAction = null;
    }

    /**
     * @function executeConfirmedAction
     * @description 执行存储在 `window.pendingConfirmAction` 中的确认操作。
     * @returns {void}
     */
    function executeConfirmedAction() {
       if (window.pendingConfirmAction) {
         window.pendingConfirmAction();
         hideConfirmModal();
       }


     /**
      * @function handleAddBookSubmit
      * @description 处理添加或编辑书籍的表单提交。根据是否存在 `bookId` 决定是添加新书还是更新现有书籍。
      * @param {Event} event - 提交事件对象。
      * @returns {Promise<void>} 无返回值。
      */
    async function handleAddBookSubmit(event) {
       event.preventDefault();

       const bookId = document.getElementById('edit-book-id').value;
       const bookName = document.getElementById('modal_book_name').value;
       const author = document.getElementById('modal_author').value;
       const type = document.getElementById('modal_type').value;
       const status = document.getElementById('modal_status').value;
       const channel = document.getElementById('modal_channel').value;
       const startDate = document.getElementById('modal_start_time').value;
       const endDate = document.getElementById('modal_end_time').value;
       const wordCount = document.getElementById('modal_word_count').value;
       const rating = document.getElementById('modal_rating').value;
       const comment = document.getElementById('modal_comment').value;

       if (!bookName || !author || !status) {
         showErrorMessage('书名、作者和状态为必填项。');
         return;
       }

       showLoadingOverlay('正在保存书籍...');

       const fields = {
         '书名': bookName,
         '作者': author,
         '分类': type,
         '状态': status,
         '渠道': channel,
         '开始时间': startDate ? new Date(startDate).getTime() / 1000 : undefined,
         '结束时间': endDate ? new Date(endDate).getTime() / 1000 : undefined,
         '字数': parseFloat(wordCount) || undefined,
         '评分': parseFloat(rating) || undefined,
         '备注': comment
       };

       const { token, datasheetId } = API_CONFIG;
       let url = `https://api.vika.cn/fusion/v1/datasheets/${datasheetId}/records`;
       let method = 'POST';
       let body = { records: [{ fields }] };

       if (bookId) {
         // 编辑现有书籍
         url = `${url}/${bookId}`;
         method = 'PUT';
         body = { fields };
       }

       try {
         const response = await fetch(url, {
           method: method,
           headers: {
             'Authorization': `Bearer ${token}`,
             'Content-Type': 'application/json'
           },
           body: JSON.stringify(body)
         });

         if (response.ok) {
           showSuccessMessage(`书籍${bookId ? '更新' : '添加'}成功！`);
           hideAddBookModal();
           await loadBooks(); // 重新加载书籍数据
         } else {
           const errorData = await response.json();
           throw new Error(`API错误: ${errorData.message || response.statusText}`);
         }
       } catch (error) {
         console.error('保存书籍失败:', error);
         showErrorMessage(`保存书籍失败: ${error.message}`);
       } finally {
         hideLoadingOverlay();
       }
     }


    function filterBooks() {
      const searchTerm = searchInput.value.toLowerCase();
      const statusValue = statusFilter.value;
      const channelValue = channelFilter.value;

      filteredBooks = allBooks.filter(book => {
        const fields = book.fields;

        // 搜索匹配
        const matchesSearch = !searchTerm ||
          (fields.book_name && fields.book_name.toLowerCase().includes(searchTerm)) ||
          (fields.author && fields.author.toLowerCase().includes(searchTerm));

        // 状态匹配
        const matchesStatus = !statusValue || fields.status === statusValue;

        // 渠道匹配
        const matchesChannel = !channelValue || fields.channel === channelValue;

        return matchesSearch && matchesStatus && matchesChannel;
      });

      currentPage = 1;
      renderBooks();
      updateStatistics();
    }

    /**
     * @function clearFilters
     * @description 清除所有筛选条件（搜索词、状态和渠道），并重置书籍列表为所有书籍。
     * @returns {void}
     */
    function clearFilters() {
      searchInput.value = '';
      statusFilter.value = '';
      channelFilter.value = '';
      filteredBooks = [...allBooks];
      currentPage = 1;
      renderBooks();
      updateStatistics();
    }

    /**
     * @function updateStatistics
     * @description 更新页面上的书籍统计信息，包括总数、已完成、在读、已弃、本月完成书籍数量和总字数。
     * @returns {void}
     */
    function updateStatistics() {
      const total = allBooks.length;
      const completed = allBooks.filter(book => book.fields.status === '1').length;
      const reading = allBooks.filter(book => book.fields.status === '0').length;
      const abandoned = allBooks.filter(book => book.fields.status === '2').length;

      // 计算本月完成的书籍
      const currentMonth = new Date().getMonth();
      const currentYear = new Date().getFullYear();
      const monthly = allBooks.filter(book => {
        if (book.fields.status !== '1' || !book.fields.end_time) return false;
        const endDate = new Date(parseInt(book.fields.end_time) * 1000);
        return endDate.getMonth() === currentMonth && endDate.getFullYear() === currentYear;
      }).length;

      // 计算总字数（单位：万字）
      let totalWordCount = 0;
      allBooks.forEach(book => {
        const wordCount = parseFloat(book.fields.number_font);
        if (!isNaN(wordCount)) {
          totalWordCount += wordCount;
        }
      });

      document.getElementById('total-books').textContent = total;
      document.getElementById('completed-books').textContent = completed;
      document.getElementById('reading-books').textContent = reading;
      document.getElementById('abandoned-books').textContent = abandoned;
      document.getElementById('monthly-books').textContent = monthly;
      document.getElementById('books-count').textContent = `(${filteredBooks.length} 本书)`;
      document.getElementById('total-word-count').textContent = totalWordCount.toFixed(1);
    }

    /**
     * @function getStatusBadgeClass
     * @description 根据书籍状态返回对应的 Tailwind CSS 样式。
     * @param {string} status - 书籍状态码（'0': 在看, '1': 看完, '2': 弃了）。
     * @returns {string} 对应的 Tailwind CSS 样式字符串。
     */
    function getStatusBadgeClass(status) {
      switch (status) {
        case '0': return 'bg-warning/10 text-warning'; // 在看
        case '1': return 'bg-success/10 text-success'; // 看完
        case '2': return 'bg-danger/10 text-danger';   // 弃了
        default: return 'bg-gray-100 text-gray-800';
      }
    }

    /**
     * @function getStatusText
     * @description 根据书籍状态码返回对应的文本描述。
     * @param {string} status - 书籍状态码。
     * @returns {string} 状态的文本描述。
     */
    function getStatusText(status) {
      switch (status) {
        case '0': return '在看';
        case '1': return '看完';
        case '2': return '弃了';
        default: return '未知';
      }
    }

    /**
     * @function getChannelInfo
     * @description 根据渠道ID返回对应的渠道名称和 Tailwind CSS 样式。
     * @param {string} channelId - 渠道ID。
     * @returns {{name: string, color: string}} 包含渠道名称和颜色样式的对象。
     */
    function getChannelInfo(channelId) {
      return channelMap[channelId] || { name: '未知', color: 'bg-gray-100 text-gray-800' };
    }

    /**
     * @function formatDate
     * @description 格式化时间戳为日期字符串。
     * @param {number} timestamp - Unix 时间戳（秒）。
     * @returns {string} 格式化的日期字符串，如果时间戳无效则返回 'N/A'。
     */
    function formatDate(timestamp) {
      if (!timestamp) return 'N/A';
      const date = new Date(timestamp * 1000);
      return date.toLocaleDateString('zh-CN', { year: 'numeric', month: '2-digit', day: '2-digit' });
    }

    /**
     * @function calculateDays
     * @description 计算两个时间戳之间的天数。
     * @param {number} startTimestamp - 开始时间戳（秒）。
     * @param {number} endTimestamp - 结束时间戳（秒）。
     * @returns {string} 天数，如果时间戳无效则返回 'N/A'。
     */
    function calculateDays(startTimestamp, endTimestamp) {
      if (!startTimestamp || !endTimestamp) return 'N/A';
      const startDate = new Date(startTimestamp * 1000);
      const endDate = new Date(endTimestamp * 1000);
      const diffTime = Math.abs(endDate - startDate);
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      return `${diffDays} 天`;
    }



    /**
     * @function renderBooks
     * @description 渲染书籍列表。
     *              根据 `filteredBooks` 数组和当前页码，生成并显示书籍卡片。
     *              如果没有书籍，则显示空状态。
     * @returns {void}
     */
    function renderBooks() {
      const booksContainer = document.getElementById('books-container');
      booksContainer.innerHTML = ''; // 清空现有书籍

      if (filteredBooks.length === 0) {
        showEmptyState();
        renderPagination(0); // 渲染空分页
        return;
      }

      hideEmptyState();

      const startIndex = (currentPage - 1) * booksPerPage;
      const endIndex = startIndex + booksPerPage;
      const booksToRender = filteredBooks.slice(startIndex, endIndex);

      booksToRender.forEach(book => {
        const bookCard = `
          <div class="bg-white p-4 rounded-lg shadow-md flex flex-col justify-between">
            <div>
              <h3 class="text-lg font-semibold text-gray-800">${book.book_name}</h3>
              <p class="text-gray-600">作者: ${book.author}</p>
              <p class="text-gray-600">类型: ${book.type}</p>
              <p class="text-gray-600">状态: ${book.status}</p>
              <p class="text-gray-600">备注: ${book.notes || '无'}</p>
            </div>
            <div class="mt-4 flex space-x-2">
              <button onclick="editBook('${book.recordId}')" class="bg-blue-500 text-white px-3 py-1 rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">编辑</button>
              <button onclick="deleteBook('${book.recordId}')" class="bg-red-500 text-white px-3 py-1 rounded-md hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-opacity-50">删除</button>
            </div>
          </div>
        `;
        booksContainer.innerHTML += bookCard;
      });

      renderPagination(filteredBooks.length);
    }

    /**
     * @function renderPagination
     * @description 根据 `filteredBooks` 的数量和 `booksPerPage` 来渲染分页控件。
     * @returns {void}
     */
    function renderPagination() {
      const totalPages = Math.ceil(filteredBooks.length / booksPerPage);
      pagination.innerHTML = '';

      if (totalPages <= 1) {
        return;
      }

      const ul = document.createElement('ul');
      ul.className = 'flex justify-center space-x-2 mt-4';

      // Previous button
      const prevLi = document.createElement('li');
      const prevBtn = document.createElement('button');
      prevBtn.className = `px-3 py-1 rounded-lg ${currentPage === 1 ? 'bg-gray-200 text-gray-500 cursor-not-allowed' : 'bg-primary text-white hover:bg-primary/90'}`;
      prevBtn.textContent = '上一页';
      prevBtn.disabled = currentPage === 1;
      prevBtn.onclick = () => {
        if (currentPage > 1) {
          currentPage--;
          renderBooks();
        }
      };
      prevLi.appendChild(prevBtn);
      ul.appendChild(prevLi);

      // Page numbers
      for (let i = 1; i <= totalPages; i++) {
        const li = document.createElement('li');
        const btn = document.createElement('button');
        btn.className = `px-3 py-1 rounded-lg ${i === currentPage ? 'bg-primary text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`;
        btn.textContent = i;
        btn.onclick = () => {
          currentPage = i;
          renderBooks();
        };
        li.appendChild(btn);
        ul.appendChild(li);
      }

      // Next button
      const nextLi = document.createElement('li');
      const nextBtn = document.createElement('button');
      nextBtn.className = `px-3 py-1 rounded-lg ${currentPage === totalPages ? 'bg-gray-200 text-gray-500 cursor-not-allowed' : 'bg-primary text-white hover:bg-primary/90'}`;
      nextBtn.textContent = '下一页';
      nextBtn.disabled = currentPage === totalPages;
      nextBtn.onclick = () => {
        if (currentPage < totalPages) {
          currentPage++;
          renderBooks();
        }
      };
      nextLi.appendChild(nextBtn);
      ul.appendChild(nextLi);

      pagination.appendChild(ul);
    }

    /**
     * 编辑书籍。
     * 根据提供的 recordId 查找书籍数据，并填充到模态框中进行编辑。
     * @param {string} recordId - 要编辑的书籍的记录ID。
     */
    function editBook(recordId) {
      const bookToEdit = allBooks.find(book => book.recordId === recordId);
      if (!bookToEdit) {
        showErrorMessage('未找到要编辑的书籍。');
        return;
      }

      const fields = bookToEdit.fields;
      document.getElementById('edit-book-id').value = recordId;
      document.getElementById('modal_book_name').value = fields.book_name || '';
      document.getElementById('modal_author').value = fields.author || '';
      document.getElementById('modal_type').value = fields.type || '';
      document.getElementById('modal_status').value = fields.status || '0';
      document.getElementById('modal_channel').value = fields.channel || '1';
      document.getElementById('modal_start_time').value = fields.start_time ? formatDate(fields.start_time) : '';
      document.getElementById('modal_end_time').value = fields.end_time ? formatDate(fields.end_time) : '';
      document.getElementById('modal_word_count').value = fields.number_font || '';
      document.getElementById('modal_rating').value = fields.rating || '';
      document.getElementById('modal_comment').value = fields.comment || '';

      showAddBookModal();
    }

    /**
     * 删除书籍。
     * 弹出确认框，如果用户确认，则从API删除书籍并重新加载数据。
     * @param {string} recordId - 要删除的书籍的记录ID。
     */
    function deleteBook(recordId) {
      showConfirmModal('确定要删除这本书籍吗？此操作不可撤销。', async () => {
        showLoadingOverlay('正在删除书籍...');
        const { token, datasheetId } = API_CONFIG;
        try {
          const response = await fetch(`https://api.vika.cn/fusion/v1/datasheets/${datasheetId}/records/${recordId}`, {
            method: 'DELETE',
            headers: {
              'Authorization': `Bearer ${token}`
            }
          });

          if (response.ok) {
            showSuccessMessage('书籍删除成功！');
            await loadBooks(); // 重新加载书籍数据
          } else {
            const errorData = await response.json();
            throw new Error(`API错误: ${errorData.message || response.statusText}`);
          }
        } catch (error) {
          console.error('删除书籍失败:', error);
          showErrorMessage(`删除书籍失败: ${error.message}`);
        } finally {
          hideLoadingOverlay();
        }
      });
    }

    /**
     * @description 设置单个自动补全输入框的逻辑。
     * @param {string} inputId - 输入框的ID。
     * @param {string} suggestionsId - 建议列表容器的ID。
     * @param {string} dataKey - 自动补全数据在 `autocompleteData` 对象中的键名。
     */
    function setupAutocompleteInput(inputId, suggestionsId, dataKey) {
      const input = document.getElementById(inputId);
      const suggestions = document.getElementById(suggestionsId);

      input.addEventListener('input', () => {
        const value = input.value.toLowerCase();
        if (!value) {
          suggestions.innerHTML = '';
          suggestions.classList.add('hidden');
          return;
        }

        const filtered = autocompleteData[dataKey].filter(item => 
          item.toLowerCase().includes(value)
        );

        if (filtered.length === 0) {
          suggestions.innerHTML = `<div class="py-2 px-4 text-gray-500">未找到匹配项</div>`;
          suggestions.classList.remove('hidden');
          return;
        }

        suggestions.innerHTML = filtered.map(item => 
          `<div class="py-2 px-4 hover:bg-gray-100 cursor-pointer" onclick="selectSuggestion(this, '${inputId}', '${suggestionsId}')">${item}</div>`
        ).join('');

        suggestions.classList.remove('hidden');
      });

      input.addEventListener('blur', () => {
        setTimeout(() => {
          suggestions.classList.add('hidden');
        }, 200);
      });
    }

    /**
     * @function selectSuggestion
     * @description 处理自动补全建议的选择。
     * @param {HTMLElement} element - 被点击的建议元素。
     * @param {string} inputId - 对应的输入框ID。
     * @param {string} suggestionsId - 对应的建议列表容器ID。
     * @returns {void}
     */
    function selectSuggestion(element, inputId, suggestionsId) {
      document.getElementById(inputId).value = element.textContent;
      document.getElementById(suggestionsId).classList.add('hidden');
    }

    /**
     * @description 初始化页面中所有需要自动补全功能的输入框。
     *              包括书名、作者和类型。
     */
    function setupAutocomplete() {
      // 初始化自动补全输入框
      setupAutocompleteInput('modal_book_name', 'modal_book_name_suggestions', 'book_names');
      setupAutocompleteInput('modal_author', 'modal_author_suggestions', 'authors');
      setupAutocompleteInput('modal_type', 'modal_type_suggestions', 'types');
    }
    
    /**
     * 页面初始化函数。
     * 设置事件监听器，初始化自动补全，加载书籍数据，并显示缓存状态。
     */
    async function init() {
      setupEventListeners();
      setupAutocomplete();
      await loadBooks(); // 确保在显示缓存状态前加载书籍
      showCacheStatus();

      // 将“阅读”标题设置为强制刷新按钮
      const refreshButton = document.getElementById('refreshButton');
      if (refreshButton) {
          refreshButton.addEventListener('click', () => {
              refreshCache(true); // 强制刷新缓存
              showToast('缓存已强制刷新！', 'success');
          });
      }
    }

    /**
     * @function showVersionModal
     * @description 显示版本信息模态框并填充内容。
     */
    function showVersionModal() {
      const versionInfoModal = document.getElementById('versionInfoModal');
      const versionContent = document.getElementById('versionContent');
      if (versionInfoModal && versionContent) {
        versionContent.innerHTML = `
          <h3 class="text-lg font-bold mb-2">v0.0.6 20250715 版本更新</h3>
          <ul class="list-disc list-inside text-gray-700">
            <li>**修改**：弃了按钮备注逻辑优化，保留原有备注并追加新的信息。</li>
            <li>**优化**：页面顶部“阅读”标题现已作为强制刷新按钮，点击可刷新缓存。</li>
            <li>**新增**：版本信息按钮及模态框，用于展示版本更新记录。</li>
          </ul>
        `;
        versionInfoModal.classList.remove('hidden');
      }
    }

    /**
     * @function hideVersionModal
     * @description 隐藏版本信息模态框。
     */
    function hideVersionModal() {
      const versionInfoModal = document.getElementById('versionInfoModal');
      if (versionInfoModal) {
        versionInfoModal.classList.add('hidden');
      }
    }

    /**
     * 全局变量声明
     */
    let addBookModal;
    let addBookForm;

    /**
     * 页面加载完成时执行的初始化操作。
     * 包括调用 `init` 函数和设置版本信息模态框的显示与隐藏。
     */
    document.addEventListener('DOMContentLoaded', () => {
      // 初始化DOM元素
      addBookModal = document.getElementById('add-book-modal');
      addBookForm = document.getElementById('add-book-form');

      init();

      const versionInfoBtn = document.getElementById('versionInfoBtn');
      const closeVersionInfoModalBtn = document.getElementById('closeVersionInfoModal');
      const closeVersionInfoModalBottomBtn = document.getElementById('closeVersionInfoModalBottom');
      const versionModal = document.getElementById('versionInfoModal');

      if (versionInfoBtn) {
        versionInfoBtn.addEventListener('click', showVersionModal);
      }

      if (closeVersionInfoModalBtn) {
        closeVersionInfoModalBtn.addEventListener('click', hideVersionModal);
      }

      if (closeVersionInfoModalBottomBtn) {
        closeVersionInfoModalBottomBtn.addEventListener('click', hideVersionModal);
      }

      // 点击模态框外部关闭
      if (versionModal) {
        versionModal.addEventListener('click', (e) => {
          if (e.target === versionModal) {
            hideVersionModal();
          }
        });
      }
    });

  </script>
<!-- 版本信息模态框 -->
  <div id="versionInfoModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden flex items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md mx-auto">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-semibold">版本信息</h2>
        <button id="closeVersionInfoModal" class="text-gray-500 hover:text-gray-700 focus:outline-none">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg>
        </button>
      </div>
      <div id="versionContent" class="mb-4 text-gray-800">
        <!-- 版本内容将通过 JavaScript 填充 -->
      </div>
      <div class="text-right">
        <button id="closeVersionInfoModalBottom" class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">关闭</button>
      </div>
    </div>
  </div>

  <!-- Toast 容器 -->
  <div id="toast-container" class="fixed bottom-4 right-4 z-50 space-y-2"></div>
</body>
</html>
